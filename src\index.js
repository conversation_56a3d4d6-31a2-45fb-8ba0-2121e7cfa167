require('dotenv').config();
const { Client, GatewayIntentBits, Collection, Events } = require('discord.js');
const fs = require('fs');
const path = require('path');
const Database = require('./database/database');
const logger = require('./utils/logger');
const CurrencyService = require('./services/currencyService');
const AuctionService = require('./services/auctionService');
const WorkService = require('./services/workService');

class ROKAuctionBot {
    constructor() {
        // Initialize Discord client
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers
            ]
        });

        // Initialize collections
        this.client.commands = new Collection();
        this.client.cooldowns = new Collection();

        // Initialize database and services
        this.database = new Database();
        this.currencyService = null;
        this.auctionService = null;
        this.workService = null;

        // Bind methods
        this.loadCommands = this.loadCommands.bind(this);
        this.loadEvents = this.loadEvents.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    async initialize() {
        try {
            logger.info('Starting ROK Auction Bot initialization...');

            // Initialize database
            await this.database.initialize();

            // Initialize services
            this.currencyService = new CurrencyService(this.database);
            this.auctionService = new AuctionService(this.database, this.currencyService);
            this.workService = new WorkService(this.database, this.currencyService);

            // Make services available to client
            this.client.database = this.database;
            this.client.currencyService = this.currencyService;
            this.client.auctionService = this.auctionService;
            this.client.workService = this.workService;

            // Load commands and events
            await this.loadCommands();
            await this.loadEvents();

            // Set up error handlers
            this.setupErrorHandlers();

            // Login to Discord
            await this.client.login(process.env.DISCORD_TOKEN);

            logger.info('ROK Auction Bot initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize bot:', error);
            process.exit(1);
        }
    }

    async loadCommands() {
        const commandsPath = path.join(__dirname, 'commands');
        
        if (!fs.existsSync(commandsPath)) {
            logger.warn('Commands directory not found');
            return;
        }

        const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

        for (const file of commandFiles) {
            const filePath = path.join(commandsPath, file);
            try {
                const command = require(filePath);
                
                if ('data' in command && 'execute' in command) {
                    this.client.commands.set(command.data.name, command);
                    logger.info(`Loaded command: ${command.data.name}`);
                } else {
                    logger.warn(`Command at ${filePath} is missing required "data" or "execute" property`);
                }
            } catch (error) {
                logger.error(`Error loading command ${file}:`, error);
            }
        }
    }

    async loadEvents() {
        const eventsPath = path.join(__dirname, 'events');
        
        if (!fs.existsSync(eventsPath)) {
            logger.warn('Events directory not found');
            return;
        }

        const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith('.js'));

        for (const file of eventFiles) {
            const filePath = path.join(eventsPath, file);
            try {
                const event = require(filePath);
                
                if (event.once) {
                    this.client.once(event.name, (...args) => event.execute(...args));
                } else {
                    this.client.on(event.name, (...args) => event.execute(...args));
                }
                
                logger.info(`Loaded event: ${event.name}`);
            } catch (error) {
                logger.error(`Error loading event ${file}:`, error);
            }
        }
    }

    setupErrorHandlers() {
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception:', error);
            this.handleError(error);
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
            this.handleError(reason);
        });

        // Handle Discord client errors
        this.client.on('error', (error) => {
            logger.error('Discord client error:', error);
        });

        // Handle process termination
        process.on('SIGINT', () => this.shutdown('SIGINT'));
        process.on('SIGTERM', () => this.shutdown('SIGTERM'));
    }

    async handleError(error) {
        logger.error('Handling error:', error);
        
        // Attempt to notify administrators if possible
        try {
            // You can implement admin notification logic here
            // For example, send a DM to bot administrators
        } catch (notificationError) {
            logger.error('Failed to send error notification:', notificationError);
        }
    }

    async shutdown(signal) {
        logger.info(`Received ${signal}. Shutting down gracefully...`);
        
        try {
            // Stop auction timers
            if (this.auctionService) {
                await this.auctionService.stopAllTimers();
            }

            // Close database connection
            if (this.database) {
                await this.database.close();
            }

            // Destroy Discord client
            if (this.client) {
                this.client.destroy();
            }

            logger.info('Bot shutdown completed');
            process.exit(0);
        } catch (error) {
            logger.error('Error during shutdown:', error);
            process.exit(1);
        }
    }
}

// Create and initialize bot
const bot = new ROKAuctionBot();
bot.initialize().catch(error => {
    logger.error('Failed to start bot:', error);
    process.exit(1);
});

// Export for testing
module.exports = ROKAuctionBot;
