const { Events, Collection } = require('discord.js');
const logger = require('../utils/logger');
const security = require('../utils/security');
const errorHandler = require('../utils/errorHandler');

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction) {
        // Handle slash commands
        if (interaction.isChatInputCommand()) {
            await handleSlashCommand(interaction);
        }
        
        // Handle button interactions
        if (interaction.isButton()) {
            await handleButtonInteraction(interaction);
        }
        
        // Handle select menu interactions
        if (interaction.isStringSelectMenu()) {
            await handleSelectMenuInteraction(interaction);
        }
    },
};

async function handleSlashCommand(interaction) {
    const command = interaction.client.commands.get(interaction.commandName);

    if (!command) {
        logger.error(`No command matching ${interaction.commandName} was found.`);
        return;
    }

    // Security checks
    if (security.isUserBanned(interaction.user.id)) {
        return interaction.reply({
            content: '🚫 You are banned from using this bot.',
            ephemeral: true
        });
    }

    // Rate limiting check
    const rateLimitResult = security.checkRateLimit(interaction.user.id, interaction.commandName);
    if (rateLimitResult.limited) {
        return interaction.reply({
            content: `⏰ Rate limited! You can use this command again in ${rateLimitResult.timeUntilReset} seconds. (${rateLimitResult.currentCount}/${rateLimitResult.maxRequests})`,
            ephemeral: true
        });
    }

    // Check cooldowns (legacy system, kept for compatibility)
    const { cooldowns } = interaction.client;
    if (!cooldowns.has(command.data.name)) {
        cooldowns.set(command.data.name, new Collection());
    }

    const now = Date.now();
    const timestamps = cooldowns.get(command.data.name);
    const defaultCooldownDuration = 3;
    const cooldownAmount = (command.cooldown ?? defaultCooldownDuration) * 1000;

    if (timestamps.has(interaction.user.id)) {
        const expirationTime = timestamps.get(interaction.user.id) + cooldownAmount;

        if (now < expirationTime) {
            const expiredTimestamp = Math.round(expirationTime / 1000);
            return interaction.reply({
                content: `Please wait, you are on a cooldown for \`${command.data.name}\`. You can use it again <t:${expiredTimestamp}:R>.`,
                ephemeral: true
            });
        }
    }

    timestamps.set(interaction.user.id, now);
    setTimeout(() => timestamps.delete(interaction.user.id), cooldownAmount);

    try {
        // Log command execution
        logger.command(
            interaction.commandName,
            interaction.user.id,
            interaction.guild?.id,
            {
                username: interaction.user.username,
                guildName: interaction.guild?.name,
                channelName: interaction.channel?.name,
                options: interaction.options.data
            }
        );

        // Ensure user exists in database
        await interaction.client.currencyService.ensureUser(
            interaction.user.id,
            interaction.user.username,
            interaction.user.discriminator
        );

        // Execute command
        await command.execute(interaction);
    } catch (error) {
        await errorHandler.handleCommandError(interaction, error, interaction.commandName);
    }
}

async function handleButtonInteraction(interaction) {
    try {
        // Security checks
        if (security.isUserBanned(interaction.user.id)) {
            return interaction.reply({
                content: '🚫 You are banned from using this bot.',
                ephemeral: true
            });
        }

        const [action, ...params] = interaction.customId.split('_');

        switch (action) {
            case 'bid':
                await handleBidButton(interaction, params);
                break;
            case 'auction':
                await handleAuctionButton(interaction, params);
                break;
            case 'task':
                await handleTaskButton(interaction, params);
                break;
            default:
                logger.warn(`Unknown button interaction: ${interaction.customId}`);
        }
    } catch (error) {
        await errorHandler.handleInteractionError(interaction, error, 'button');
    }
}

async function handleSelectMenuInteraction(interaction) {
    try {
        const [action, ...params] = interaction.customId.split('_');
        
        switch (action) {
            case 'rank':
                await handleRankSelect(interaction, params);
                break;
            case 'task':
                await handleTaskSelect(interaction, params);
                break;
            default:
                logger.warn(`Unknown select menu interaction: ${interaction.customId}`);
        }
    } catch (error) {
        logger.error('Error handling select menu interaction:', error);
        
        const errorMessage = {
            content: 'There was an error processing your selection!',
            ephemeral: true
        };

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
}

async function handleBidButton(interaction, params) {
    const [auctionId, bidAmount] = params;
    
    // Ensure user exists in database
    await interaction.client.currencyService.ensureUser(
        interaction.user.id,
        interaction.user.username,
        interaction.user.discriminator
    );
    
    // Process bid
    const result = await interaction.client.auctionService.placeBid(
        parseInt(auctionId),
        interaction.user.id,
        parseInt(bidAmount)
    );
    
    if (result.success) {
        await interaction.reply({
            content: `✅ Your bid of **${bidAmount.toLocaleString()}** currency has been placed!`,
            ephemeral: true
        });
    } else {
        await interaction.reply({
            content: `❌ ${result.error}`,
            ephemeral: true
        });
    }
}

async function handleAuctionButton(interaction, params) {
    const [action, auctionId] = params;
    
    if (action === 'info') {
        const auction = await interaction.client.auctionService.getAuctionById(parseInt(auctionId));
        if (auction) {
            const embed = await interaction.client.auctionService.createAuctionEmbed(auction);
            await interaction.reply({ embeds: [embed], ephemeral: true });
        } else {
            await interaction.reply({
                content: 'Auction not found or has ended.',
                ephemeral: true
            });
        }
    }
}

async function handleTaskButton(interaction, params) {
    const [action, taskId] = params;
    
    if (action === 'complete') {
        const result = await interaction.client.workService.completeTask(
            interaction.user.id,
            parseInt(taskId)
        );
        
        if (result.success) {
            await interaction.reply({
                content: `✅ Task completed! You earned **${result.reward.toLocaleString()}** currency.`,
                ephemeral: true
            });
        } else {
            await interaction.reply({
                content: `❌ ${result.error}`,
                ephemeral: true
            });
        }
    }
}

async function handleRankSelect(interaction, params) {
    // Handle rank selection for auction creation
    const selectedRank = interaction.values[0];
    // Implementation would depend on specific use case
}

async function handleTaskSelect(interaction, params) {
    // Handle task selection for work management
    const selectedTask = interaction.values[0];
    // Implementation would depend on specific use case
}
