-- Users table to store Discord user information and currency
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    discord_id TEXT UNIQUE NOT NULL,
    username TEXT NOT NULL,
    discriminator TEXT,
    currency INTEGER DEFAULT 0,
    total_earned INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    last_daily_reward TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Governor ranks configuration
CREATE TABLE IF NOT EXISTS governor_ranks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rank_number INTEGER UNIQUE NOT NULL,
    rank_name TEXT NOT NULL,
    min_bid_amount INTEGER NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Auctions table to track active and completed auctions
CREATE TABLE IF NOT EXISTS auctions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rank_id INTEGER NOT NULL,
    creator_discord_id TEXT NOT NULL,
    status TEXT DEFAULT 'active' CHECK(status IN ('active', 'completed', 'cancelled')),
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    end_time DATETIME NOT NULL,
    winner_discord_id TEXT,
    winning_bid INTEGER,
    total_bids INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (rank_id) REFERENCES governor_ranks(id),
    FOREIGN KEY (creator_discord_id) REFERENCES users(discord_id),
    FOREIGN KEY (winner_discord_id) REFERENCES users(discord_id)
);

-- Bids table to track all bids placed in auctions
CREATE TABLE IF NOT EXISTS bids (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    auction_id INTEGER NOT NULL,
    bidder_discord_id TEXT NOT NULL,
    bid_amount INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    placed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    refunded_at DATETIME,
    FOREIGN KEY (auction_id) REFERENCES auctions(id),
    FOREIGN KEY (bidder_discord_id) REFERENCES users(discord_id)
);

-- Currency transactions for audit trail
CREATE TABLE IF NOT EXISTS currency_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_discord_id TEXT NOT NULL,
    transaction_type TEXT NOT NULL CHECK(transaction_type IN ('earned', 'spent', 'refund', 'admin_add', 'admin_remove', 'daily_reward')),
    amount INTEGER NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    description TEXT,
    reference_id INTEGER, -- Can reference auction_id, bid_id, etc.
    reference_type TEXT, -- 'auction', 'bid', 'task', etc.
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_discord_id) REFERENCES users(discord_id)
);

-- Work tasks (daily, weekly, monthly)
CREATE TABLE IF NOT EXISTS work_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_type TEXT NOT NULL CHECK(task_type IN ('daily', 'weekly', 'monthly')),
    title TEXT NOT NULL,
    description TEXT,
    reward_amount INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- User task completions
CREATE TABLE IF NOT EXISTS user_task_completions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_discord_id TEXT NOT NULL,
    task_id INTEGER NOT NULL,
    completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    reward_claimed BOOLEAN DEFAULT FALSE,
    period_identifier TEXT NOT NULL, -- e.g., '2024-01-15' for daily, '2024-W03' for weekly, '2024-01' for monthly
    FOREIGN KEY (user_discord_id) REFERENCES users(discord_id),
    FOREIGN KEY (task_id) REFERENCES work_tasks(id),
    UNIQUE(user_discord_id, task_id, period_identifier)
);

-- Bot configuration settings
CREATE TABLE IF NOT EXISTS bot_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_discord_id ON users(discord_id);
CREATE INDEX IF NOT EXISTS idx_auctions_status ON auctions(status);
CREATE INDEX IF NOT EXISTS idx_auctions_end_time ON auctions(end_time);
CREATE INDEX IF NOT EXISTS idx_bids_auction_id ON bids(auction_id);
CREATE INDEX IF NOT EXISTS idx_bids_bidder ON bids(bidder_discord_id);
CREATE INDEX IF NOT EXISTS idx_currency_transactions_user ON currency_transactions(user_discord_id);
CREATE INDEX IF NOT EXISTS idx_currency_transactions_type ON currency_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_user ON user_task_completions(user_discord_id);
CREATE INDEX IF NOT EXISTS idx_user_task_completions_period ON user_task_completions(period_identifier);
