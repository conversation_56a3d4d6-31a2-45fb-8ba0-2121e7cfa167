const { Embed<PERSON><PERSON>er, ActionRowBuilder, But<PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');

/**
 * Utility class for creating consistent, beautiful Discord embeds
 */
class CustomEmbedBuilder {
    constructor() {
        this.colors = {
            success: 0x00ff00,
            error: 0xff0000,
            warning: 0xffaa00,
            info: 0x00aaff,
            auction: 0x9932cc,
            currency: 0xffd700,
            rank: 0x8b4513,
            premium: 0xff6b6b
        };

        this.emojis = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            currency: '💰',
            auction: '🏛️',
            rank: '🎯',
            timer: '⏰',
            user: '👤',
            crown: '👑',
            trophy: '🏆',
            fire: '🔥',
            star: '⭐',
            gem: '💎',
            shield: '🛡️',
            sword: '⚔️',
            bow: '🏹',
            magic: '✨',
            lightning: '⚡'
        };
    }

    /**
     * Create a success embed
     */
    createSuccessEmbed(title, description, fields = []) {
        const embed = new EmbedBuilder()
            .setTitle(`${this.emojis.success} ${title}`)
            .setDescription(description)
            .setColor(this.colors.success)
            .setTimestamp();

        if (fields.length > 0) {
            embed.addFields(fields);
        }

        return embed;
    }

    /**
     * Create an error embed
     */
    createErrorEmbed(title, description, fields = []) {
        const embed = new EmbedBuilder()
            .setTitle(`${this.emojis.error} ${title}`)
            .setDescription(description)
            .setColor(this.colors.error)
            .setTimestamp();

        if (fields.length > 0) {
            embed.addFields(fields);
        }

        return embed;
    }

    /**
     * Create a warning embed
     */
    createWarningEmbed(title, description, fields = []) {
        const embed = new EmbedBuilder()
            .setTitle(`${this.emojis.warning} ${title}`)
            .setDescription(description)
            .setColor(this.colors.warning)
            .setTimestamp();

        if (fields.length > 0) {
            embed.addFields(fields);
        }

        return embed;
    }

    /**
     * Create an info embed
     */
    createInfoEmbed(title, description, fields = []) {
        const embed = new EmbedBuilder()
            .setTitle(`${this.emojis.info} ${title}`)
            .setDescription(description)
            .setColor(this.colors.info)
            .setTimestamp();

        if (fields.length > 0) {
            embed.addFields(fields);
        }

        return embed;
    }

    /**
     * Create a premium auction embed with enhanced styling
     */
    createPremiumAuctionEmbed(auction) {
        const timeRemaining = this.getTimeRemaining(auction.end_time);
        const isExpired = new Date() > new Date(auction.end_time);
        const isHighValue = auction.min_bid_amount >= 30000;
        
        // Determine color based on auction status and value
        let color = this.colors.auction;
        if (isExpired) color = this.colors.error;
        else if (auction.winning_bid) color = this.colors.success;
        else if (isHighValue) color = this.colors.premium;

        // Select appropriate emojis based on rank
        const rankEmoji = this.getRankEmoji(auction.rank_number);
        const statusEmoji = isExpired ? '🔴' : (auction.winning_bid ? '🟢' : '🟡');

        const embed = new EmbedBuilder()
            .setTitle(`${rankEmoji} ${auction.rank_name} Auction ${statusEmoji}`)
            .setDescription(`${this.emojis.auction} **Governor Position Auction**\n${auction.description || 'Compete for this prestigious governor rank!'}`)
            .setColor(color)
            .addFields(
                {
                    name: `${this.emojis.rank} Rank Details`,
                    value: `**Position:** #${auction.rank_number}\n**Title:** ${auction.rank_name}`,
                    inline: true
                },
                {
                    name: `${this.emojis.currency} Bidding Info`,
                    value: `**Minimum:** ${auction.min_bid_amount.toLocaleString()} currency\n**Current:** ${auction.winning_bid ? `${auction.winning_bid.toLocaleString()} currency` : 'No bids yet'}`,
                    inline: true
                },
                {
                    name: `${this.emojis.timer} Time Status`,
                    value: `**Remaining:** ${isExpired ? '**ENDED**' : timeRemaining}\n**Status:** ${isExpired ? 'Completed' : 'Active'}`,
                    inline: true
                },
                {
                    name: `${this.emojis.crown} Current Leader`,
                    value: auction.winner_username ? 
                        `${this.emojis.trophy} **${auction.winner_username}**` : 
                        `${this.emojis.info} No leader yet`,
                    inline: true
                },
                {
                    name: `${this.emojis.fire} Activity`,
                    value: `**Total Bids:** ${auction.total_bids}\n**Participants:** ${auction.bids ? auction.bids.length : 0}`,
                    inline: true
                },
                {
                    name: `${this.emojis.user} Creator`,
                    value: `${auction.creator_username || 'Unknown'}`,
                    inline: true
                }
            );

        // Add bid history with enhanced formatting
        if (auction.bids && auction.bids.length > 0) {
            const topBids = auction.bids.slice(0, 5);
            const bidHistory = topBids.map((bid, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                return `${medal} **${bid.username}** - ${bid.bid_amount.toLocaleString()} ${this.emojis.currency}`;
            }).join('\n');
            
            embed.addFields({
                name: `${this.emojis.trophy} Top Bidders`,
                value: bidHistory,
                inline: false
            });
        }

        // Add special indicators for high-value auctions
        if (isHighValue) {
            embed.addFields({
                name: `${this.emojis.gem} Premium Auction`,
                value: `${this.emojis.star} This is a high-value governor position!`,
                inline: false
            });
        }

        embed.setFooter({ 
            text: `Auction ID: ${auction.id} | Rise of Kingdoms Governor System`,
            iconURL: 'https://cdn.discordapp.com/emojis/123456789.png' // You can add a custom icon here
        })
        .setTimestamp(new Date(auction.end_time));

        return embed;
    }

    /**
     * Create enhanced currency display embed
     */
    createCurrencyEmbed(user, stats, isOwnBalance = true) {
        const embed = new EmbedBuilder()
            .setTitle(`${this.emojis.currency} ${isOwnBalance ? 'Your' : `${user.username}'s`} Currency Portfolio`)
            .setColor(this.colors.currency)
            .setThumbnail(user.displayAvatarURL({ dynamic: true, size: 256 }))
            .setDescription(`${this.emojis.gem} **Financial Overview**`);

        // Main balance section
        embed.addFields(
            {
                name: `${this.emojis.currency} Current Balance`,
                value: `**${stats.currency.toLocaleString()}** currency`,
                inline: true
            },
            {
                name: `${this.emojis.fire} Total Earned`,
                value: `**${stats.total_earned.toLocaleString()}** currency`,
                inline: true
            },
            {
                name: `${this.emojis.lightning} Total Spent`,
                value: `**${stats.total_spent.toLocaleString()}** currency`,
                inline: true
            }
        );

        // Performance metrics
        const netWorth = stats.total_earned - stats.total_spent;
        const efficiency = stats.total_earned > 0 ? ((netWorth / stats.total_earned) * 100).toFixed(1) : 0;

        embed.addFields(
            {
                name: `${this.emojis.trophy} Net Worth`,
                value: `**${netWorth.toLocaleString()}** currency`,
                inline: true
            },
            {
                name: `${this.emojis.star} Efficiency`,
                value: `**${efficiency}%** retention`,
                inline: true
            },
            {
                name: `${this.emojis.magic} Activity`,
                value: `**${stats.transactionCount}** transactions`,
                inline: true
            }
        );

        return embed;
    }

    /**
     * Create notification embed for auction events
     */
    createAuctionNotificationEmbed(type, auction, user = null, amount = null) {
        let title, description, color, emoji;

        switch (type) {
            case 'bid_placed':
                title = 'New Bid Placed!';
                description = `${user.username} placed a bid of **${amount.toLocaleString()}** currency on **${auction.rank_name}**`;
                color = this.colors.info;
                emoji = this.emojis.fire;
                break;
            case 'auction_won':
                title = 'Auction Won!';
                description = `${this.emojis.crown} **${user.username}** has won the **${auction.rank_name}** position!`;
                color = this.colors.success;
                emoji = this.emojis.trophy;
                break;
            case 'auction_ended':
                title = 'Auction Ended';
                description = `The auction for **${auction.rank_name}** has concluded.`;
                color = this.colors.warning;
                emoji = this.emojis.timer;
                break;
            case 'outbid':
                title = 'You\'ve Been Outbid!';
                description = `Someone has placed a higher bid on **${auction.rank_name}**. Your currency has been refunded.`;
                color = this.colors.warning;
                emoji = this.emojis.warning;
                break;
            default:
                title = 'Auction Update';
                description = 'An auction event has occurred.';
                color = this.colors.info;
                emoji = this.emojis.info;
        }

        const embed = new EmbedBuilder()
            .setTitle(`${emoji} ${title}`)
            .setDescription(description)
            .setColor(color)
            .addFields(
                {
                    name: `${this.emojis.rank} Auction Details`,
                    value: `**Rank:** #${auction.rank_number} - ${auction.rank_name}\n**ID:** ${auction.id}`,
                    inline: true
                }
            )
            .setTimestamp();

        if (amount) {
            embed.addFields({
                name: `${this.emojis.currency} Amount`,
                value: `${amount.toLocaleString()} currency`,
                inline: true
            });
        }

        return embed;
    }

    /**
     * Get appropriate emoji for rank number
     */
    getRankEmoji(rankNumber) {
        if (rankNumber <= 3) return this.emojis.crown;
        if (rankNumber <= 7) return this.emojis.gem;
        if (rankNumber <= 10) return this.emojis.shield;
        if (rankNumber <= 13) return this.emojis.sword;
        return this.emojis.bow;
    }

    /**
     * Get time remaining formatted string
     */
    getTimeRemaining(endTime) {
        const now = new Date();
        const end = new Date(endTime);
        const diff = end - now;

        if (diff <= 0) return 'Ended';

        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);

        if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
        if (minutes > 0) return `${minutes}m ${seconds}s`;
        return `${seconds}s`;
    }

    /**
     * Create action buttons for auctions
     */
    createAuctionActionRow(auction, quickBidAmounts = []) {
        const isExpired = new Date() > new Date(auction.end_time);
        
        if (isExpired) return null;

        const buttons = [];

        // Quick bid buttons
        if (quickBidAmounts.length === 0) {
            quickBidAmounts = [
                auction.min_bid_amount,
                auction.min_bid_amount + 5000,
                auction.min_bid_amount + 10000
            ];

            if (auction.winning_bid) {
                quickBidAmounts[0] = auction.winning_bid + 1000;
                quickBidAmounts[1] = auction.winning_bid + 5000;
                quickBidAmounts[2] = auction.winning_bid + 10000;
            }
        }

        quickBidAmounts.slice(0, 3).forEach((amount, index) => {
            buttons.push(
                new ButtonBuilder()
                    .setCustomId(`bid_${auction.id}_${amount}`)
                    .setLabel(`${amount.toLocaleString()}`)
                    .setStyle(index === 0 ? ButtonStyle.Success : ButtonStyle.Primary)
                    .setEmoji(this.emojis.currency)
            );
        });

        // Info button
        buttons.push(
            new ButtonBuilder()
                .setCustomId(`auction_info_${auction.id}`)
                .setLabel('Details')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji(this.emojis.info)
        );

        return new ActionRowBuilder().addComponents(buttons);
    }
}

module.exports = new CustomEmbedBuilder();
