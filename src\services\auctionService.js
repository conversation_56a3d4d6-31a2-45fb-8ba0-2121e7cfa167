const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionR<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const logger = require('../utils/logger');
const moment = require('moment');

class AuctionService {
    constructor(database, currencyService) {
        this.database = database;
        this.currencyService = currencyService;
        this.activeTimers = new Map(); // Store auction timers
        this.minAuctionDuration = parseInt(process.env.AUCTION_MIN_DURATION) || 300000; // 5 minutes
        this.maxAuctionDuration = parseInt(process.env.AUCTION_MAX_DURATION) || 3600000; // 1 hour
        this.defaultAuctionDuration = parseInt(process.env.DEFAULT_AUCTION_DURATION) || 1800000; // 30 minutes
    }

    /**
     * Get all governor ranks
     */
    async getGovernorRanks() {
        try {
            return await this.database.all(
                'SELECT * FROM governor_ranks ORDER BY rank_number ASC'
            );
        } catch (error) {
            logger.error('<PERSON>rror getting governor ranks:', error);
            throw error;
        }
    }

    /**
     * Get governor rank by rank number
     */
    async getGovernorRank(rankNumber) {
        try {
            return await this.database.get(
                'SELECT * FROM governor_ranks WHERE rank_number = ?',
                [rankNumber]
            );
        } catch (error) {
            logger.error('Error getting governor rank:', error);
            throw error;
        }
    }

    /**
     * Create a new auction
     */
    async createAuction(rankNumber, creatorDiscordId, durationMs = null) {
        try {
            const duration = durationMs || this.defaultAuctionDuration;
            
            if (duration < this.minAuctionDuration || duration > this.maxAuctionDuration) {
                throw new Error(`Auction duration must be between ${this.minAuctionDuration/1000} and ${this.maxAuctionDuration/1000} seconds`);
            }

            // Check if rank exists
            const rank = await this.getGovernorRank(rankNumber);
            if (!rank) {
                throw new Error('Invalid governor rank');
            }

            // Check if there's already an active auction for this rank
            const existingAuction = await this.database.get(
                'SELECT id FROM auctions WHERE rank_id = ? AND status = "active"',
                [rank.id]
            );

            if (existingAuction) {
                throw new Error('An auction for this rank is already active');
            }

            const endTime = new Date(Date.now() + duration);

            // Create auction
            const result = await this.database.run(
                `INSERT INTO auctions (rank_id, creator_discord_id, end_time, status) 
                 VALUES (?, ?, ?, 'active')`,
                [rank.id, creatorDiscordId, endTime.toISOString()]
            );

            const auctionId = result.id;

            // Set up timer for auction end
            this.setAuctionTimer(auctionId, duration);

            logger.auction('created', auctionId, creatorDiscordId, {
                rankNumber,
                duration,
                endTime: endTime.toISOString()
            });

            return {
                success: true,
                auctionId,
                rank,
                endTime
            };
        } catch (error) {
            logger.error('Error creating auction:', error);
            throw error;
        }
    }

    /**
     * Place a bid on an auction
     */
    async placeBid(auctionId, bidderDiscordId, bidAmount) {
        try {
            await this.database.beginTransaction();

            // Get auction details
            const auction = await this.database.get(
                `SELECT a.*, gr.rank_number, gr.rank_name, gr.min_bid_amount 
                 FROM auctions a 
                 JOIN governor_ranks gr ON a.rank_id = gr.id 
                 WHERE a.id = ? AND a.status = 'active'`,
                [auctionId]
            );

            if (!auction) {
                await this.database.rollback();
                return { success: false, error: 'Auction not found or has ended' };
            }

            // Check if auction has expired
            if (new Date() > new Date(auction.end_time)) {
                await this.database.rollback();
                return { success: false, error: 'Auction has expired' };
            }

            // Check minimum bid amount
            if (bidAmount < auction.min_bid_amount) {
                await this.database.rollback();
                return { 
                    success: false, 
                    error: `Minimum bid for this rank is ${auction.min_bid_amount.toLocaleString()} currency` 
                };
            }

            // Check if user has sufficient currency
            const userBalance = await this.currencyService.getBalance(bidderDiscordId);
            if (userBalance < bidAmount) {
                await this.database.rollback();
                return { success: false, error: 'Insufficient currency' };
            }

            // Check if user already has an active bid
            const existingBid = await this.database.get(
                'SELECT * FROM bids WHERE auction_id = ? AND bidder_discord_id = ? AND is_active = TRUE',
                [auctionId, bidderDiscordId]
            );

            if (existingBid) {
                // Refund previous bid
                await this.currencyService.addCurrency(
                    bidderDiscordId,
                    existingBid.bid_amount,
                    'refund',
                    `Bid refund for auction ${auctionId}`,
                    auctionId,
                    'auction'
                );

                // Deactivate previous bid
                await this.database.run(
                    'UPDATE bids SET is_active = FALSE WHERE id = ?',
                    [existingBid.id]
                );
            }

            // Deduct currency for new bid
            await this.currencyService.removeCurrency(
                bidderDiscordId,
                bidAmount,
                'spent',
                `Bid placed on auction ${auctionId}`,
                auctionId,
                'auction'
            );

            // Place new bid
            await this.database.run(
                `INSERT INTO bids (auction_id, bidder_discord_id, bid_amount, is_active) 
                 VALUES (?, ?, ?, TRUE)`,
                [auctionId, bidderDiscordId, bidAmount]
            );

            // Update auction with new highest bid if applicable
            const currentHighestBid = await this.database.get(
                'SELECT MAX(bid_amount) as highest_bid FROM bids WHERE auction_id = ? AND is_active = TRUE',
                [auctionId]
            );

            if (bidAmount >= currentHighestBid.highest_bid) {
                await this.database.run(
                    'UPDATE auctions SET winning_bid = ?, winner_discord_id = ?, total_bids = total_bids + 1 WHERE id = ?',
                    [bidAmount, bidderDiscordId, auctionId]
                );
            } else {
                await this.database.run(
                    'UPDATE auctions SET total_bids = total_bids + 1 WHERE id = ?',
                    [auctionId]
                );
            }

            await this.database.commit();

            logger.auction('bid_placed', auctionId, bidderDiscordId, {
                bidAmount,
                rankNumber: auction.rank_number
            });

            return { success: true };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error placing bid:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get auction by ID with full details
     */
    async getAuctionById(auctionId) {
        try {
            const auction = await this.database.get(
                `SELECT a.*, gr.rank_number, gr.rank_name, gr.min_bid_amount, gr.description,
                        u.username as creator_username, w.username as winner_username
                 FROM auctions a 
                 JOIN governor_ranks gr ON a.rank_id = gr.id 
                 LEFT JOIN users u ON a.creator_discord_id = u.discord_id
                 LEFT JOIN users w ON a.winner_discord_id = w.discord_id
                 WHERE a.id = ?`,
                [auctionId]
            );

            if (auction) {
                // Get current bids
                auction.bids = await this.database.all(
                    `SELECT b.*, u.username 
                     FROM bids b 
                     JOIN users u ON b.bidder_discord_id = u.discord_id 
                     WHERE b.auction_id = ? AND b.is_active = TRUE 
                     ORDER BY b.bid_amount DESC`,
                    [auctionId]
                );
            }

            return auction;
        } catch (error) {
            logger.error('Error getting auction by ID:', error);
            throw error;
        }
    }

    /**
     * Get active auctions
     */
    async getActiveAuctions() {
        try {
            return await this.database.all(
                `SELECT a.*, gr.rank_number, gr.rank_name, gr.min_bid_amount
                 FROM auctions a
                 JOIN governor_ranks gr ON a.rank_id = gr.id
                 WHERE a.status = 'active'
                 ORDER BY a.end_time ASC`
            );
        } catch (error) {
            logger.error('Error getting active auctions:', error);
            throw error;
        }
    }

    /**
     * Set auction timer
     */
    setAuctionTimer(auctionId, durationMs) {
        const timer = setTimeout(async () => {
            try {
                await this.endAuction(auctionId);
                this.activeTimers.delete(auctionId);
            } catch (error) {
                logger.error(`Error ending auction ${auctionId}:`, error);
            }
        }, durationMs);

        this.activeTimers.set(auctionId, timer);
    }

    /**
     * End an auction
     */
    async endAuction(auctionId) {
        try {
            await this.database.beginTransaction();

            const auction = await this.getAuctionById(auctionId);
            if (!auction || auction.status !== 'active') {
                await this.database.rollback();
                return;
            }

            // Update auction status
            await this.database.run(
                'UPDATE auctions SET status = "completed", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [auctionId]
            );

            // Refund all losing bids
            const losingBids = await this.database.all(
                'SELECT * FROM bids WHERE auction_id = ? AND is_active = TRUE AND bidder_discord_id != ?',
                [auctionId, auction.winner_discord_id || '']
            );

            for (const bid of losingBids) {
                await this.currencyService.addCurrency(
                    bid.bidder_discord_id,
                    bid.bid_amount,
                    'refund',
                    `Auction ${auctionId} ended - bid refund`,
                    auctionId,
                    'auction'
                );

                await this.database.run(
                    'UPDATE bids SET is_active = FALSE, refunded_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [bid.id]
                );
            }

            await this.database.commit();

            logger.auction('ended', auctionId, auction.winner_discord_id, {
                rankNumber: auction.rank_number,
                winningBid: auction.winning_bid,
                totalBids: auction.total_bids
            });

            return auction;
        } catch (error) {
            await this.database.rollback();
            logger.error('Error ending auction:', error);
            throw error;
        }
    }

    /**
     * Process expired auctions
     */
    async processExpiredAuctions() {
        try {
            const expiredAuctions = await this.database.all(
                'SELECT id FROM auctions WHERE status = "active" AND end_time <= CURRENT_TIMESTAMP'
            );

            for (const auction of expiredAuctions) {
                await this.endAuction(auction.id);
                this.activeTimers.delete(auction.id);
            }
        } catch (error) {
            logger.error('Error processing expired auctions:', error);
        }
    }

    /**
     * Cancel an auction
     */
    async cancelAuction(auctionId, cancellerDiscordId) {
        try {
            await this.database.beginTransaction();

            const auction = await this.getAuctionById(auctionId);
            if (!auction || auction.status !== 'active') {
                await this.database.rollback();
                throw new Error('Auction not found or already ended');
            }

            // Update auction status
            await this.database.run(
                'UPDATE auctions SET status = "cancelled", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [auctionId]
            );

            // Refund all active bids
            const activeBids = await this.database.all(
                'SELECT * FROM bids WHERE auction_id = ? AND is_active = TRUE',
                [auctionId]
            );

            for (const bid of activeBids) {
                await this.currencyService.addCurrency(
                    bid.bidder_discord_id,
                    bid.bid_amount,
                    'refund',
                    `Auction ${auctionId} cancelled - bid refund`,
                    auctionId,
                    'auction'
                );

                await this.database.run(
                    'UPDATE bids SET is_active = FALSE, refunded_at = CURRENT_TIMESTAMP WHERE id = ?',
                    [bid.id]
                );
            }

            // Clear timer
            if (this.activeTimers.has(auctionId)) {
                clearTimeout(this.activeTimers.get(auctionId));
                this.activeTimers.delete(auctionId);
            }

            await this.database.commit();

            logger.auction('cancelled', auctionId, cancellerDiscordId, {
                rankNumber: auction.rank_number
            });

            return { success: true };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error cancelling auction:', error);
            throw error;
        }
    }

    /**
     * Create auction embed for Discord
     */
    async createAuctionEmbed(auction) {
        const timeRemaining = this.getTimeRemaining(auction.end_time);
        const isExpired = new Date() > new Date(auction.end_time);

        const embed = new EmbedBuilder()
            .setTitle(`🏛️ ${auction.rank_name} Auction`)
            .setDescription(auction.description || 'Governor position auction')
            .setColor(isExpired ? 0xff0000 : (auction.winning_bid ? 0x00ff00 : 0xffff00))
            .addFields(
                {
                    name: '🎯 Rank',
                    value: `#${auction.rank_number} - ${auction.rank_name}`,
                    inline: true
                },
                {
                    name: '💰 Minimum Bid',
                    value: `${auction.min_bid_amount.toLocaleString()} currency`,
                    inline: true
                },
                {
                    name: '🏆 Current Highest Bid',
                    value: auction.winning_bid ?
                        `${auction.winning_bid.toLocaleString()} currency` :
                        'No bids yet',
                    inline: true
                },
                {
                    name: '👑 Current Leader',
                    value: auction.winner_username || 'None',
                    inline: true
                },
                {
                    name: '📊 Total Bids',
                    value: auction.total_bids.toString(),
                    inline: true
                },
                {
                    name: '⏰ Time Remaining',
                    value: isExpired ? '**ENDED**' : timeRemaining,
                    inline: true
                }
            )
            .setFooter({
                text: `Auction ID: ${auction.id} | Created by ${auction.creator_username}`
            })
            .setTimestamp(new Date(auction.end_time));

        // Add bid history if available
        if (auction.bids && auction.bids.length > 0) {
            const topBids = auction.bids.slice(0, 5);
            const bidHistory = topBids.map((bid, index) =>
                `${index + 1}. **${bid.username}** - ${bid.bid_amount.toLocaleString()} currency`
            ).join('\n');

            embed.addFields({
                name: '📈 Top Bids',
                value: bidHistory,
                inline: false
            });
        }

        return embed;
    }

    /**
     * Create action row with bid buttons
     */
    createAuctionButtons(auction) {
        const isExpired = new Date() > new Date(auction.end_time);

        if (isExpired) {
            return null;
        }

        const quickBidAmounts = [
            auction.min_bid_amount,
            auction.min_bid_amount + 5000,
            auction.min_bid_amount + 10000
        ];

        if (auction.winning_bid) {
            quickBidAmounts[0] = auction.winning_bid + 1000;
            quickBidAmounts[1] = auction.winning_bid + 5000;
            quickBidAmounts[2] = auction.winning_bid + 10000;
        }

        const buttons = quickBidAmounts.map((amount, index) =>
            new ButtonBuilder()
                .setCustomId(`bid_${auction.id}_${amount}`)
                .setLabel(`Bid ${amount.toLocaleString()}`)
                .setStyle(ButtonStyle.Primary)
                .setEmoji('💰')
        );

        buttons.push(
            new ButtonBuilder()
                .setCustomId(`auction_info_${auction.id}`)
                .setLabel('Auction Info')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('ℹ️')
        );

        return new ActionRowBuilder().addComponents(buttons);
    }

    /**
     * Get time remaining for auction
     */
    getTimeRemaining(endTime) {
        const now = moment();
        const end = moment(endTime);
        const duration = moment.duration(end.diff(now));

        if (duration.asMilliseconds() <= 0) {
            return 'Ended';
        }

        const hours = Math.floor(duration.asHours());
        const minutes = duration.minutes();
        const seconds = duration.seconds();

        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Stop all active timers
     */
    async stopAllTimers() {
        for (const [auctionId, timer] of this.activeTimers) {
            clearTimeout(timer);
            logger.info(`Cleared timer for auction ${auctionId}`);
        }
        this.activeTimers.clear();
    }

    /**
     * Restart timers for active auctions (used on bot restart)
     */
    async restartActiveTimers() {
        try {
            const activeAuctions = await this.getActiveAuctions();

            for (const auction of activeAuctions) {
                const timeRemaining = new Date(auction.end_time) - new Date();

                if (timeRemaining > 0) {
                    this.setAuctionTimer(auction.id, timeRemaining);
                    logger.info(`Restarted timer for auction ${auction.id}`);
                } else {
                    // Auction should have ended, process it
                    await this.endAuction(auction.id);
                }
            }
        } catch (error) {
            logger.error('Error restarting active timers:', error);
        }
    }
}

module.exports = AuctionService;
