const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const logger = require('../utils/logger');
const embedBuilder = require('../utils/embedBuilder');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('tasks')
        .setDescription('View and manage your work tasks')
        .addStringOption(option =>
            option.setName('type')
                .setDescription('Task type to view')
                .setRequired(false)
                .addChoices(
                    { name: 'Daily', value: 'daily' },
                    { name: 'Weekly', value: 'weekly' },
                    { name: 'Monthly', value: 'monthly' },
                    { name: 'All', value: 'all' }
                )),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const taskType = interaction.options.getString('type') || 'all';

            await interaction.deferReply({ ephemeral: true });

            // Ensure user exists in database
            await interaction.client.currencyService.ensureUser(
                interaction.user.id,
                interaction.user.username,
                interaction.user.discriminator
            );

            // Get user's progress
            const progress = await interaction.client.workService.getUserProgress(interaction.user.id);

            if (taskType === 'all') {
                // Show overview of all task types
                const embed = new EmbedBuilder()
                    .setTitle('📋 Your Work Tasks Overview')
                    .setColor(0x00aaff)
                    .setThumbnail(interaction.user.displayAvatarURL())
                    .setDescription('Track your daily, weekly, and monthly progress!')
                    .setTimestamp();

                // Daily progress
                const dailyProgress = progress.daily;
                const dailyPercentage = dailyProgress.total > 0 ? Math.round((dailyProgress.completed / dailyProgress.total) * 100) : 0;
                embed.addFields({
                    name: '📅 Daily Tasks',
                    value: `**Progress:** ${dailyProgress.completed}/${dailyProgress.total} (${dailyPercentage}%)\n` +
                           `**Rewards:** ${dailyProgress.earnedReward.toLocaleString()}/${dailyProgress.totalReward.toLocaleString()} currency\n` +
                           `**Period:** ${dailyProgress.period}`,
                    inline: true
                });

                // Weekly progress
                const weeklyProgress = progress.weekly;
                const weeklyPercentage = weeklyProgress.total > 0 ? Math.round((weeklyProgress.completed / weeklyProgress.total) * 100) : 0;
                embed.addFields({
                    name: '📊 Weekly Tasks',
                    value: `**Progress:** ${weeklyProgress.completed}/${weeklyProgress.total} (${weeklyPercentage}%)\n` +
                           `**Rewards:** ${weeklyProgress.earnedReward.toLocaleString()}/${weeklyProgress.totalReward.toLocaleString()} currency\n` +
                           `**Period:** Week ${weeklyProgress.period.split('W')[1]}`,
                    inline: true
                });

                // Monthly progress
                const monthlyProgress = progress.monthly;
                const monthlyPercentage = monthlyProgress.total > 0 ? Math.round((monthlyProgress.completed / monthlyProgress.total) * 100) : 0;
                embed.addFields({
                    name: '📈 Monthly Tasks',
                    value: `**Progress:** ${monthlyProgress.completed}/${monthlyProgress.total} (${monthlyPercentage}%)\n` +
                           `**Rewards:** ${monthlyProgress.earnedReward.toLocaleString()}/${monthlyProgress.totalReward.toLocaleString()} currency\n` +
                           `**Period:** ${monthlyProgress.period}`,
                    inline: true
                });

                // Total statistics
                const totalCompleted = dailyProgress.completed + weeklyProgress.completed + monthlyProgress.completed;
                const totalTasks = dailyProgress.total + weeklyProgress.total + monthlyProgress.total;
                const totalEarned = dailyProgress.earnedReward + weeklyProgress.earnedReward + monthlyProgress.earnedReward;
                const totalPossible = dailyProgress.totalReward + weeklyProgress.totalReward + monthlyProgress.totalReward;

                embed.addFields({
                    name: '📊 Overall Statistics',
                    value: `**Total Completed:** ${totalCompleted}/${totalTasks} tasks\n` +
                           `**Total Earned:** ${totalEarned.toLocaleString()}/${totalPossible.toLocaleString()} currency\n` +
                           `**Completion Rate:** ${totalTasks > 0 ? Math.round((totalCompleted / totalTasks) * 100) : 0}%`,
                    inline: false
                });

                // Create buttons for detailed views
                const actionRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('tasks_daily')
                            .setLabel('Daily Tasks')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('📅'),
                        new ButtonBuilder()
                            .setCustomId('tasks_weekly')
                            .setLabel('Weekly Tasks')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('📊'),
                        new ButtonBuilder()
                            .setCustomId('tasks_monthly')
                            .setLabel('Monthly Tasks')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('📈')
                    );

                await interaction.editReply({
                    embeds: [embed],
                    components: [actionRow]
                });

            } else {
                // Show detailed view for specific task type
                const taskProgress = progress[taskType];
                
                if (!taskProgress) {
                    return await interaction.editReply({
                        content: '❌ Invalid task type specified.',
                    });
                }

                const embed = new EmbedBuilder()
                    .setTitle(`📋 ${taskType.charAt(0).toUpperCase() + taskType.slice(1)} Tasks`)
                    .setColor(taskType === 'daily' ? 0x00ff00 : taskType === 'weekly' ? 0x0099ff : 0xff6600)
                    .setThumbnail(interaction.user.displayAvatarURL())
                    .setDescription(`Your ${taskType} task progress for period: **${taskProgress.period}**`)
                    .setTimestamp();

                // Progress summary
                const percentage = taskProgress.total > 0 ? Math.round((taskProgress.completed / taskProgress.total) * 100) : 0;
                embed.addFields({
                    name: '📊 Progress Summary',
                    value: `**Completed:** ${taskProgress.completed}/${taskProgress.total} tasks (${percentage}%)\n` +
                           `**Rewards Earned:** ${taskProgress.earnedReward.toLocaleString()}/${taskProgress.totalReward.toLocaleString()} currency`,
                    inline: false
                });

                // Individual tasks
                if (taskProgress.tasks.length > 0) {
                    const taskList = taskProgress.tasks.map(task => {
                        const status = task.completed ? '✅' : '⏳';
                        const reward = task.reward_amount > 0 ? ` (${task.reward_amount.toLocaleString()} currency)` : '';
                        return `${status} **${task.title}**${reward}\n${task.description}`;
                    }).join('\n\n');

                    embed.addFields({
                        name: '📝 Task List',
                        value: taskList.length > 1024 ? taskList.substring(0, 1021) + '...' : taskList,
                        inline: false
                    });
                } else {
                    embed.addFields({
                        name: '📝 Task List',
                        value: 'No tasks available for this period.',
                        inline: false
                    });
                }

                // Create complete task buttons for incomplete tasks
                const incompleteTasks = taskProgress.tasks.filter(task => !task.completed);
                if (incompleteTasks.length > 0) {
                    const buttons = incompleteTasks.slice(0, 5).map(task => 
                        new ButtonBuilder()
                            .setCustomId(`task_complete_${task.id}`)
                            .setLabel(`Complete: ${task.title.substring(0, 20)}${task.title.length > 20 ? '...' : ''}`)
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('✅')
                    );

                    const actionRow = new ActionRowBuilder().addComponents(buttons);
                    
                    await interaction.editReply({
                        embeds: [embed],
                        components: [actionRow]
                    });
                } else {
                    await interaction.editReply({ embeds: [embed] });
                }
            }

        } catch (error) {
            logger.error('Error in tasks command:', error);
            
            const errorMessage = {
                content: '❌ An error occurred while fetching task information.',
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ ...errorMessage, ephemeral: true });
            }
        }
    },
};
