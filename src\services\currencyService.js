const logger = require('../utils/logger');
const moment = require('moment');

class CurrencyService {
    constructor(database) {
        this.database = database;
        this.maxCurrencyPerUser = parseInt(process.env.MAX_CURRENCY_PER_USER) || 10000000;
        this.dailyRewardAmount = parseInt(process.env.DAILY_REWARD_AMOUNT) || 5000;
    }

    /**
     * Ensure user exists in database
     */
    async ensureUser(discordId, username, discriminator = null) {
        try {
            let user = await this.database.get(
                'SELECT * FROM users WHERE discord_id = ?',
                [discordId]
            );

            if (!user) {
                await this.database.run(
                    `INSERT INTO users (discord_id, username, discriminator, currency, total_earned, total_spent) 
                     VALUES (?, ?, ?, 0, 0, 0)`,
                    [discordId, username, discriminator]
                );
                
                user = await this.database.get(
                    'SELECT * FROM users WHERE discord_id = ?',
                    [discordId]
                );
                
                logger.info(`New user created: ${username} (${discordId})`);
            } else {
                // Update username if changed
                if (user.username !== username || user.discriminator !== discriminator) {
                    await this.database.run(
                        'UPDATE users SET username = ?, discriminator = ?, updated_at = CURRENT_TIMESTAMP WHERE discord_id = ?',
                        [username, discriminator, discordId]
                    );
                }
            }

            return user;
        } catch (error) {
            logger.error('Error ensuring user exists:', error);
            throw error;
        }
    }

    /**
     * Get user's current currency balance
     */
    async getBalance(discordId) {
        try {
            const user = await this.database.get(
                'SELECT currency FROM users WHERE discord_id = ?',
                [discordId]
            );
            
            return user ? user.currency : 0;
        } catch (error) {
            logger.error('Error getting user balance:', error);
            throw error;
        }
    }

    /**
     * Add currency to user's account
     */
    async addCurrency(discordId, amount, transactionType = 'earned', description = '', referenceId = null, referenceType = null) {
        if (amount <= 0) {
            throw new Error('Amount must be positive');
        }

        try {
            await this.database.beginTransaction();

            const user = await this.database.get(
                'SELECT currency, total_earned FROM users WHERE discord_id = ?',
                [discordId]
            );

            if (!user) {
                throw new Error('User not found');
            }

            const newBalance = user.currency + amount;
            
            // Check maximum currency limit
            if (newBalance > this.maxCurrencyPerUser) {
                await this.database.rollback();
                throw new Error(`Cannot exceed maximum currency limit of ${this.maxCurrencyPerUser.toLocaleString()}`);
            }

            // Update user balance and total earned
            await this.database.run(
                'UPDATE users SET currency = ?, total_earned = ?, updated_at = CURRENT_TIMESTAMP WHERE discord_id = ?',
                [newBalance, user.total_earned + amount, discordId]
            );

            // Record transaction
            await this.database.run(
                `INSERT INTO currency_transactions 
                 (user_discord_id, transaction_type, amount, balance_before, balance_after, description, reference_id, reference_type) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [discordId, transactionType, amount, user.currency, newBalance, description, referenceId, referenceType]
            );

            await this.database.commit();

            logger.currency('add', discordId, amount, newBalance, {
                transactionType,
                description,
                referenceId,
                referenceType
            });

            return {
                success: true,
                newBalance,
                amount
            };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error adding currency:', error);
            throw error;
        }
    }

    /**
     * Remove currency from user's account
     */
    async removeCurrency(discordId, amount, transactionType = 'spent', description = '', referenceId = null, referenceType = null) {
        if (amount <= 0) {
            throw new Error('Amount must be positive');
        }

        try {
            await this.database.beginTransaction();

            const user = await this.database.get(
                'SELECT currency, total_spent FROM users WHERE discord_id = ?',
                [discordId]
            );

            if (!user) {
                await this.database.rollback();
                throw new Error('User not found');
            }

            if (user.currency < amount) {
                await this.database.rollback();
                throw new Error('Insufficient currency');
            }

            const newBalance = user.currency - amount;

            // Update user balance and total spent
            await this.database.run(
                'UPDATE users SET currency = ?, total_spent = ?, updated_at = CURRENT_TIMESTAMP WHERE discord_id = ?',
                [newBalance, user.total_spent + amount, discordId]
            );

            // Record transaction
            await this.database.run(
                `INSERT INTO currency_transactions
                 (user_discord_id, transaction_type, amount, balance_before, balance_after, description, reference_id, reference_type)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [discordId, transactionType, -amount, user.currency, newBalance, description, referenceId, referenceType]
            );

            await this.database.commit();

            logger.currency('remove', discordId, amount, newBalance, {
                transactionType,
                description,
                referenceId,
                referenceType
            });

            return {
                success: true,
                newBalance,
                amount
            };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error removing currency:', error);
            throw error;
        }
    }

    /**
     * Transfer currency between users
     */
    async transferCurrency(fromDiscordId, toDiscordId, amount, description = '') {
        if (amount <= 0) {
            throw new Error('Amount must be positive');
        }

        try {
            await this.database.beginTransaction();

            // Remove from sender
            await this.removeCurrency(fromDiscordId, amount, 'transfer_out', `Transfer to user: ${description}`, null, 'transfer');
            
            // Add to receiver
            await this.addCurrency(toDiscordId, amount, 'transfer_in', `Transfer from user: ${description}`, null, 'transfer');

            await this.database.commit();

            logger.currency('transfer', fromDiscordId, amount, null, {
                toUser: toDiscordId,
                description
            });

            return { success: true };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error transferring currency:', error);
            throw error;
        }
    }

    /**
     * Check if user can claim daily reward
     */
    async canClaimDailyReward(discordId) {
        try {
            const user = await this.database.get(
                'SELECT last_daily_reward FROM users WHERE discord_id = ?',
                [discordId]
            );

            if (!user) {
                return false;
            }

            if (!user.last_daily_reward) {
                return true;
            }

            const lastClaim = moment(user.last_daily_reward);
            const now = moment();
            
            return !lastClaim.isSame(now, 'day');
        } catch (error) {
            logger.error('Error checking daily reward eligibility:', error);
            throw error;
        }
    }

    /**
     * Claim daily reward
     */
    async claimDailyReward(discordId) {
        try {
            const canClaim = await this.canClaimDailyReward(discordId);
            
            if (!canClaim) {
                throw new Error('Daily reward already claimed today');
            }

            await this.database.beginTransaction();

            // Add currency
            await this.addCurrency(
                discordId,
                this.dailyRewardAmount,
                'daily_reward',
                'Daily reward claim'
            );

            // Update last claim time
            await this.database.run(
                'UPDATE users SET last_daily_reward = CURRENT_TIMESTAMP WHERE discord_id = ?',
                [discordId]
            );

            await this.database.commit();

            return {
                success: true,
                amount: this.dailyRewardAmount
            };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error claiming daily reward:', error);
            throw error;
        }
    }

    /**
     * Get user's transaction history
     */
    async getTransactionHistory(discordId, limit = 10, offset = 0) {
        try {
            const transactions = await this.database.all(
                `SELECT * FROM currency_transactions 
                 WHERE user_discord_id = ? 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?`,
                [discordId, limit, offset]
            );

            return transactions;
        } catch (error) {
            logger.error('Error getting transaction history:', error);
            throw error;
        }
    }

    /**
     * Get currency leaderboard
     */
    async getLeaderboard(limit = 10) {
        try {
            const leaderboard = await this.database.all(
                `SELECT discord_id, username, currency, total_earned 
                 FROM users 
                 ORDER BY currency DESC 
                 LIMIT ?`,
                [limit]
            );

            return leaderboard;
        } catch (error) {
            logger.error('Error getting leaderboard:', error);
            throw error;
        }
    }

    /**
     * Get user statistics
     */
    async getUserStats(discordId) {
        try {
            const user = await this.database.get(
                'SELECT * FROM users WHERE discord_id = ?',
                [discordId]
            );

            if (!user) {
                throw new Error('User not found');
            }

            const transactionCount = await this.database.get(
                'SELECT COUNT(*) as count FROM currency_transactions WHERE user_discord_id = ?',
                [discordId]
            );

            return {
                ...user,
                transactionCount: transactionCount.count
            };
        } catch (error) {
            logger.error('Error getting user stats:', error);
            throw error;
        }
    }
}

module.exports = CurrencyService;
