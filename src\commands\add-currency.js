const { Slash<PERSON>ommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('add-currency')
        .setDescription('Add currency to a user (Admin only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to add currency to')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount of currency to add')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(1000000))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for adding currency')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('user');
            const amount = interaction.options.getInteger('amount');
            const reason = interaction.options.getString('reason') || 'Admin currency addition';

            // Check if user has admin permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ You need Administrator permissions to use this command.',
                    ephemeral: true
                });
            }

            // Ensure target user exists in database
            await interaction.client.currencyService.ensureUser(
                targetUser.id,
                targetUser.username,
                targetUser.discriminator
            );

            // Add currency
            const result = await interaction.client.currencyService.addCurrency(
                targetUser.id,
                amount,
                'admin_add',
                reason,
                null,
                'admin_command'
            );

            if (result.success) {
                const embed = new EmbedBuilder()
                    .setTitle('💰 Currency Added Successfully')
                    .setColor(0x00ff00)
                    .setDescription(`Added **${amount.toLocaleString()}** currency to ${targetUser}`)
                    .addFields(
                        {
                            name: '👤 Target User',
                            value: `${targetUser.username}#${targetUser.discriminator}`,
                            inline: true
                        },
                        {
                            name: '💵 Amount Added',
                            value: `${amount.toLocaleString()} currency`,
                            inline: true
                        },
                        {
                            name: '💳 New Balance',
                            value: `${result.newBalance.toLocaleString()} currency`,
                            inline: true
                        },
                        {
                            name: '📝 Reason',
                            value: reason,
                            inline: false
                        }
                    )
                    .setFooter({
                        text: `Command executed by ${interaction.user.username}`,
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await interaction.reply({ embeds: [embed] });

                // Log the admin action
                logger.currency('admin_add', targetUser.id, amount, result.newBalance, {
                    adminId: interaction.user.id,
                    adminUsername: interaction.user.username,
                    reason,
                    guildId: interaction.guild.id
                });

                // Send notification to target user if they're not the same as the admin
                if (targetUser.id !== interaction.user.id) {
                    try {
                        const dmEmbed = new EmbedBuilder()
                            .setTitle('💰 Currency Received')
                            .setColor(0x00ff00)
                            .setDescription(`You have received **${amount.toLocaleString()}** currency!`)
                            .addFields(
                                {
                                    name: '💵 Amount',
                                    value: `${amount.toLocaleString()} currency`,
                                    inline: true
                                },
                                {
                                    name: '💳 New Balance',
                                    value: `${result.newBalance.toLocaleString()} currency`,
                                    inline: true
                                },
                                {
                                    name: '📝 Reason',
                                    value: reason,
                                    inline: false
                                }
                            )
                            .setFooter({
                                text: `From: ${interaction.guild.name}`,
                                iconURL: interaction.guild.iconURL()
                            })
                            .setTimestamp();

                        await targetUser.send({ embeds: [dmEmbed] });
                    } catch (dmError) {
                        logger.warn(`Could not send DM to user ${targetUser.id}:`, dmError);
                    }
                }
            } else {
                await interaction.reply({
                    content: `❌ Failed to add currency: ${result.error}`,
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Error in add-currency command:', error);
            
            const errorMessage = {
                content: '❌ An error occurred while adding currency. Please try again.',
                ephemeral: true
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    },
};
