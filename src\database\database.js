const sqlite3 = require('sqlite3').verbose();
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');

class Database {
    constructor(dbPath = process.env.DATABASE_PATH || './data/bot.db') {
        this.dbPath = dbPath;
        this.db = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            // Ensure data directory exists
            const dataDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dataDir)) {
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // Connect to database
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    logger.error('Error opening database:', err);
                    throw err;
                }
                logger.info('Connected to SQLite database');
            });

            // Enable foreign keys
            await this.run('PRAGMA foreign_keys = ON');
            
            // Create tables
            await this.createTables();
            
            // Insert default data
            await this.insertDefaultData();
            
            this.isInitialized = true;
            logger.info('Database initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize database:', error);
            throw error;
        }
    }

    async createTables() {
        const schemaPath = path.join(__dirname, 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        
        // Split schema by semicolons and execute each statement
        const statements = schema.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
            await this.run(statement);
        }
    }

    async insertDefaultData() {
        // Insert governor ranks
        const ranks = [
            { rank_number: 1, rank_name: 'Supreme Governor', min_bid_amount: 50000, description: 'Highest ranking governor position' },
            { rank_number: 2, rank_name: 'Grand Governor', min_bid_amount: 45000, description: 'Second highest ranking position' },
            { rank_number: 3, rank_name: 'Elite Governor', min_bid_amount: 40000, description: 'Elite tier governor' },
            { rank_number: 4, rank_name: 'Senior Governor', min_bid_amount: 35000, description: 'Senior level governor' },
            { rank_number: 5, rank_name: 'Advanced Governor', min_bid_amount: 30000, description: 'Advanced tier governor' },
            { rank_number: 6, rank_name: 'Experienced Governor', min_bid_amount: 25000, description: 'Experienced governor' },
            { rank_number: 7, rank_name: 'Skilled Governor', min_bid_amount: 20000, description: 'Skilled tier governor' },
            { rank_number: 8, rank_name: 'Competent Governor', min_bid_amount: 18000, description: 'Competent level governor' },
            { rank_number: 9, rank_name: 'Regular Governor', min_bid_amount: 16000, description: 'Regular tier governor' },
            { rank_number: 10, rank_name: 'Junior Governor', min_bid_amount: 14000, description: 'Junior level governor' },
            { rank_number: 11, rank_name: 'Apprentice Governor', min_bid_amount: 12000, description: 'Apprentice tier governor' },
            { rank_number: 12, rank_name: 'Novice Governor', min_bid_amount: 10000, description: 'Novice level governor' },
            { rank_number: 13, rank_name: 'Trainee Governor', min_bid_amount: 8000, description: 'Trainee tier governor' },
            { rank_number: 14, rank_name: 'Cadet Governor', min_bid_amount: 6000, description: 'Cadet level governor' },
            { rank_number: 15, rank_name: 'Recruit Governor', min_bid_amount: 5000, description: 'Entry level governor position' }
        ];

        for (const rank of ranks) {
            await this.run(
                `INSERT OR IGNORE INTO governor_ranks (rank_number, rank_name, min_bid_amount, description) 
                 VALUES (?, ?, ?, ?)`,
                [rank.rank_number, rank.rank_name, rank.min_bid_amount, rank.description]
            );
        }

        // Insert default work tasks
        const defaultTasks = [
            { task_type: 'daily', title: 'Daily Login', description: 'Log in to the game daily', reward_amount: 1000 },
            { task_type: 'daily', title: 'Complete Daily Quests', description: 'Complete all daily quests', reward_amount: 2000 },
            { task_type: 'weekly', title: 'Weekly Alliance Contribution', description: 'Contribute to alliance weekly goals', reward_amount: 10000 },
            { task_type: 'weekly', title: 'Participate in Events', description: 'Actively participate in weekly events', reward_amount: 15000 },
            { task_type: 'monthly', title: 'Monthly Leadership Review', description: 'Complete monthly leadership assessment', reward_amount: 50000 }
        ];

        for (const task of defaultTasks) {
            await this.run(
                `INSERT OR IGNORE INTO work_tasks (task_type, title, description, reward_amount) 
                 VALUES (?, ?, ?, ?)`,
                [task.task_type, task.title, task.description, task.reward_amount]
            );
        }

        // Insert default bot settings
        const defaultSettings = [
            { setting_key: 'daily_reward_amount', setting_value: '5000', description: 'Amount of currency given for daily reward' },
            { setting_key: 'max_currency_per_user', setting_value: '10000000', description: 'Maximum currency a user can hold' },
            { setting_key: 'auction_channel_id', setting_value: '', description: 'Channel ID for auction announcements' }
        ];

        for (const setting of defaultSettings) {
            await this.run(
                `INSERT OR IGNORE INTO bot_settings (setting_key, setting_value, description) 
                 VALUES (?, ?, ?)`,
                [setting.setting_key, setting.setting_value, setting.description]
            );
        }
    }

    // Promisify database operations
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    logger.error('Database run error:', err);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    logger.error('Database get error:', err);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    logger.error('Database all error:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        logger.error('Error closing database:', err);
                        reject(err);
                    } else {
                        logger.info('Database connection closed');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    // Transaction support
    async beginTransaction() {
        await this.run('BEGIN TRANSACTION');
    }

    async commit() {
        await this.run('COMMIT');
    }

    async rollback() {
        await this.run('ROLLBACK');
    }
}

module.exports = Database;
