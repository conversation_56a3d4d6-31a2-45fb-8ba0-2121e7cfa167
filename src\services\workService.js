const logger = require('../utils/logger');
const moment = require('moment');

class WorkService {
    constructor(database, currencyService) {
        this.database = database;
        this.currencyService = currencyService;
    }

    /**
     * Get all work tasks by type
     */
    async getTasksByType(taskType) {
        try {
            return await this.database.all(
                'SELECT * FROM work_tasks WHERE task_type = ? AND is_active = TRUE ORDER BY id ASC',
                [taskType]
            );
        } catch (error) {
            logger.error('Error getting tasks by type:', error);
            throw error;
        }
    }

    /**
     * Get user's task completions for a specific period
     */
    async getUserTaskCompletions(discordId, taskType, periodIdentifier) {
        try {
            return await this.database.all(
                `SELECT utc.*, wt.title, wt.description, wt.reward_amount 
                 FROM user_task_completions utc 
                 JOIN work_tasks wt ON utc.task_id = wt.id 
                 WHERE utc.user_discord_id = ? AND wt.task_type = ? AND utc.period_identifier = ?`,
                [discordId, taskType, periodIdentifier]
            );
        } catch (error) {
            logger.error('Error getting user task completions:', error);
            throw error;
        }
    }

    /**
     * Complete a task for a user
     */
    async completeTask(discordId, taskId) {
        try {
            await this.database.beginTransaction();

            // Get task details
            const task = await this.database.get(
                'SELECT * FROM work_tasks WHERE id = ? AND is_active = TRUE',
                [taskId]
            );

            if (!task) {
                await this.database.rollback();
                return { success: false, error: 'Task not found or inactive' };
            }

            // Generate period identifier
            const periodIdentifier = this.generatePeriodIdentifier(task.task_type);

            // Check if user already completed this task in this period
            const existingCompletion = await this.database.get(
                'SELECT id FROM user_task_completions WHERE user_discord_id = ? AND task_id = ? AND period_identifier = ?',
                [discordId, taskId, periodIdentifier]
            );

            if (existingCompletion) {
                await this.database.rollback();
                return { success: false, error: 'Task already completed for this period' };
            }

            // Record task completion
            await this.database.run(
                `INSERT INTO user_task_completions (user_discord_id, task_id, period_identifier, reward_claimed) 
                 VALUES (?, ?, ?, TRUE)`,
                [discordId, taskId, periodIdentifier]
            );

            // Award currency if there's a reward
            if (task.reward_amount > 0) {
                await this.currencyService.addCurrency(
                    discordId,
                    task.reward_amount,
                    'task_reward',
                    `Completed task: ${task.title}`,
                    taskId,
                    'work_task'
                );
            }

            await this.database.commit();

            logger.info(`Task completed: ${task.title} by user ${discordId}`);

            return {
                success: true,
                task,
                reward: task.reward_amount,
                periodIdentifier
            };
        } catch (error) {
            await this.database.rollback();
            logger.error('Error completing task:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get user's progress for all task types
     */
    async getUserProgress(discordId) {
        try {
            const now = moment();
            const progress = {
                daily: {
                    period: now.format('YYYY-MM-DD'),
                    tasks: [],
                    completed: 0,
                    total: 0,
                    totalReward: 0,
                    earnedReward: 0
                },
                weekly: {
                    period: now.format('YYYY-[W]WW'),
                    tasks: [],
                    completed: 0,
                    total: 0,
                    totalReward: 0,
                    earnedReward: 0
                },
                monthly: {
                    period: now.format('YYYY-MM'),
                    tasks: [],
                    completed: 0,
                    total: 0,
                    totalReward: 0,
                    earnedReward: 0
                }
            };

            // Get tasks and completions for each type
            for (const taskType of ['daily', 'weekly', 'monthly']) {
                const tasks = await this.getTasksByType(taskType);
                const completions = await this.getUserTaskCompletions(
                    discordId,
                    taskType,
                    progress[taskType].period
                );

                progress[taskType].tasks = tasks.map(task => {
                    const completion = completions.find(c => c.task_id === task.id);
                    return {
                        ...task,
                        completed: !!completion,
                        completedAt: completion ? completion.completed_at : null
                    };
                });

                progress[taskType].total = tasks.length;
                progress[taskType].completed = completions.length;
                progress[taskType].totalReward = tasks.reduce((sum, task) => sum + task.reward_amount, 0);
                progress[taskType].earnedReward = completions.reduce((sum, comp) => sum + comp.reward_amount, 0);
            }

            return progress;
        } catch (error) {
            logger.error('Error getting user progress:', error);
            throw error;
        }
    }

    /**
     * Generate period identifier based on task type
     */
    generatePeriodIdentifier(taskType) {
        const now = moment();
        
        switch (taskType) {
            case 'daily':
                return now.format('YYYY-MM-DD');
            case 'weekly':
                return now.format('YYYY-[W]WW');
            case 'monthly':
                return now.format('YYYY-MM');
            default:
                throw new Error('Invalid task type');
        }
    }

    /**
     * Reset daily tasks (called by cron job)
     */
    async resetDailyTasks() {
        try {
            logger.info('Starting daily task reset...');
            
            // Get yesterday's date
            const yesterday = moment().subtract(1, 'day').format('YYYY-MM-DD');
            
            // Log completion statistics
            const completionStats = await this.database.get(
                `SELECT COUNT(*) as total_completions, COUNT(DISTINCT user_discord_id) as unique_users
                 FROM user_task_completions utc
                 JOIN work_tasks wt ON utc.task_id = wt.id
                 WHERE wt.task_type = 'daily' AND utc.period_identifier = ?`,
                [yesterday]
            );

            logger.info(`Daily reset complete. Yesterday's stats: ${completionStats.total_completions} completions by ${completionStats.unique_users} users`);
            
            return { success: true, stats: completionStats };
        } catch (error) {
            logger.error('Error resetting daily tasks:', error);
            throw error;
        }
    }

    /**
     * Reset weekly tasks (called by cron job)
     */
    async resetWeeklyTasks() {
        try {
            logger.info('Starting weekly task reset...');
            
            // Get last week's identifier
            const lastWeek = moment().subtract(1, 'week').format('YYYY-[W]WW');
            
            // Log completion statistics
            const completionStats = await this.database.get(
                `SELECT COUNT(*) as total_completions, COUNT(DISTINCT user_discord_id) as unique_users
                 FROM user_task_completions utc
                 JOIN work_tasks wt ON utc.task_id = wt.id
                 WHERE wt.task_type = 'weekly' AND utc.period_identifier = ?`,
                [lastWeek]
            );

            logger.info(`Weekly reset complete. Last week's stats: ${completionStats.total_completions} completions by ${completionStats.unique_users} users`);
            
            return { success: true, stats: completionStats };
        } catch (error) {
            logger.error('Error resetting weekly tasks:', error);
            throw error;
        }
    }

    /**
     * Reset monthly tasks (called by cron job)
     */
    async resetMonthlyTasks() {
        try {
            logger.info('Starting monthly task reset...');
            
            // Get last month's identifier
            const lastMonth = moment().subtract(1, 'month').format('YYYY-MM');
            
            // Log completion statistics
            const completionStats = await this.database.get(
                `SELECT COUNT(*) as total_completions, COUNT(DISTINCT user_discord_id) as unique_users
                 FROM user_task_completions utc
                 JOIN work_tasks wt ON utc.task_id = wt.id
                 WHERE wt.task_type = 'monthly' AND utc.period_identifier = ?`,
                [lastMonth]
            );

            logger.info(`Monthly reset complete. Last month's stats: ${completionStats.total_completions} completions by ${completionStats.unique_users} users`);
            
            return { success: true, stats: completionStats };
        } catch (error) {
            logger.error('Error resetting monthly tasks:', error);
            throw error;
        }
    }

    /**
     * Create a new work task
     */
    async createTask(taskType, title, description, rewardAmount) {
        try {
            const result = await this.database.run(
                `INSERT INTO work_tasks (task_type, title, description, reward_amount, is_active) 
                 VALUES (?, ?, ?, ?, TRUE)`,
                [taskType, title, description, rewardAmount]
            );

            logger.info(`New task created: ${title} (${taskType})`);
            
            return { success: true, taskId: result.id };
        } catch (error) {
            logger.error('Error creating task:', error);
            throw error;
        }
    }

    /**
     * Update a work task
     */
    async updateTask(taskId, updates) {
        try {
            const allowedFields = ['title', 'description', 'reward_amount', 'is_active'];
            const updateFields = [];
            const updateValues = [];

            for (const [field, value] of Object.entries(updates)) {
                if (allowedFields.includes(field)) {
                    updateFields.push(`${field} = ?`);
                    updateValues.push(value);
                }
            }

            if (updateFields.length === 0) {
                throw new Error('No valid fields to update');
            }

            updateValues.push(taskId);

            await this.database.run(
                `UPDATE work_tasks SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
                updateValues
            );

            logger.info(`Task updated: ${taskId}`);
            
            return { success: true };
        } catch (error) {
            logger.error('Error updating task:', error);
            throw error;
        }
    }

    /**
     * Get leaderboard for task completions
     */
    async getTaskLeaderboard(taskType, periodIdentifier, limit = 10) {
        try {
            return await this.database.all(
                `SELECT u.discord_id, u.username, COUNT(*) as completed_tasks, 
                        SUM(wt.reward_amount) as total_rewards
                 FROM user_task_completions utc
                 JOIN users u ON utc.user_discord_id = u.discord_id
                 JOIN work_tasks wt ON utc.task_id = wt.id
                 WHERE wt.task_type = ? AND utc.period_identifier = ?
                 GROUP BY u.discord_id, u.username
                 ORDER BY completed_tasks DESC, total_rewards DESC
                 LIMIT ?`,
                [taskType, periodIdentifier, limit]
            );
        } catch (error) {
            logger.error('Error getting task leaderboard:', error);
            throw error;
        }
    }
}

module.exports = WorkService;
