const { Events } = require('discord.js');
const logger = require('../utils/logger');
const cron = require('node-cron');

module.exports = {
    name: Events.ClientReady,
    once: true,
    async execute(client) {
        logger.info(`Ready! Logged in as ${client.user.tag}`);
        
        // Set bot status
        client.user.setActivity('Rise of Kingdoms Auctions', { type: 'WATCHING' });

        // Start auction cleanup task (runs every minute)
        cron.schedule('* * * * *', async () => {
            try {
                await client.auctionService.processExpiredAuctions();
            } catch (error) {
                logger.error('Error processing expired auctions:', error);
            }
        });

        // Start daily reset task (runs at midnight)
        cron.schedule('0 0 * * *', async () => {
            try {
                await client.workService.resetDailyTasks();
                logger.info('Daily tasks reset completed');
            } catch (error) {
                logger.error('Error resetting daily tasks:', error);
            }
        });

        // Start weekly reset task (runs on Monday at midnight)
        cron.schedule('0 0 * * 1', async () => {
            try {
                await client.workService.resetWeeklyTasks();
                logger.info('Weekly tasks reset completed');
            } catch (error) {
                logger.error('Error resetting weekly tasks:', error);
            }
        });

        // Start monthly reset task (runs on 1st of month at midnight)
        cron.schedule('0 0 1 * *', async () => {
            try {
                await client.workService.resetMonthlyTasks();
                logger.info('Monthly tasks reset completed');
            } catch (error) {
                logger.error('Error resetting monthly tasks:', error);
            }
        });

        logger.info('All scheduled tasks initialized');
    },
};
