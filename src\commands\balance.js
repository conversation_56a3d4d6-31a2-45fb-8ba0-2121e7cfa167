const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('balance')
        .setDescription('Check your or another user\'s currency balance')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('User to check balance for (optional)')
                .setRequired(false)),
    
    cooldown: 3,

    async execute(interaction) {
        try {
            const targetUser = interaction.options.getUser('user') || interaction.user;
            const isOwnBalance = targetUser.id === interaction.user.id;

            await interaction.deferReply({ ephemeral: isOwnBalance });

            // Ensure target user exists in database
            await interaction.client.currencyService.ensureUser(
                targetUser.id,
                targetUser.username,
                targetUser.discriminator
            );

            // Get user stats
            const userStats = await interaction.client.currencyService.getUserStats(targetUser.id);

            if (!userStats) {
                return await interaction.editReply({
                    content: '❌ User not found in the database.',
                });
            }

            // Get user's rank on leaderboard
            const leaderboard = await interaction.client.currencyService.getLeaderboard(100);
            const userRank = leaderboard.findIndex(user => user.discord_id === targetUser.id) + 1;

            const embed = new EmbedBuilder()
                .setTitle(`💰 ${isOwnBalance ? 'Your' : `${targetUser.username}'s`} Currency Balance`)
                .setColor(0x00aaff)
                .setThumbnail(targetUser.displayAvatarURL())
                .addFields(
                    {
                        name: '💳 Current Balance',
                        value: `${userStats.currency.toLocaleString()} currency`,
                        inline: true
                    },
                    {
                        name: '📈 Total Earned',
                        value: `${userStats.total_earned.toLocaleString()} currency`,
                        inline: true
                    },
                    {
                        name: '📉 Total Spent',
                        value: `${userStats.total_spent.toLocaleString()} currency`,
                        inline: true
                    },
                    {
                        name: '🏆 Leaderboard Rank',
                        value: userRank > 0 ? `#${userRank}` : 'Unranked',
                        inline: true
                    },
                    {
                        name: '📊 Transactions',
                        value: `${userStats.transactionCount} total`,
                        inline: true
                    },
                    {
                        name: '📅 Member Since',
                        value: `<t:${Math.floor(new Date(userStats.created_at).getTime() / 1000)}:D>`,
                        inline: true
                    }
                );

            // Add last daily reward info if it's the user's own balance
            if (isOwnBalance && userStats.last_daily_reward) {
                const lastDaily = new Date(userStats.last_daily_reward);
                embed.addFields({
                    name: '🎁 Last Daily Reward',
                    value: `<t:${Math.floor(lastDaily.getTime() / 1000)}:R>`,
                    inline: true
                });
            }

            embed.setFooter({
                text: `${targetUser.username}#${targetUser.discriminator}`,
                iconURL: targetUser.displayAvatarURL()
            }).setTimestamp();

            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            logger.error('Error in balance command:', error);
            
            const errorMessage = {
                content: '❌ An error occurred while fetching balance information.',
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ ...errorMessage, ephemeral: true });
            }
        }
    },
};
