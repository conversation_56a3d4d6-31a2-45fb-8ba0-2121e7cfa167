{"name": "rok-governor-auction-bot", "version": "1.0.0", "description": "Discord bot with comprehensive work management and currency-based auction system for Rise of Kingdoms governor positions", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "deploy": "node src/deploy-commands.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["discord", "bot", "rise-of-kingdoms", "auction", "currency", "work-management"], "author": "Your Name", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "moment": "^2.29.4", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "eslint": "^8.56.0", "jest": "^29.7.0"}, "engines": {"node": ">=16.0.0"}}