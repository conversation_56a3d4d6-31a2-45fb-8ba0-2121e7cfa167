const { <PERSON><PERSON><PERSON><PERSON>mand<PERSON>uilder, <PERSON>bed<PERSON><PERSON>er, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('auctions')
        .setDescription('View active auctions')
        .addStringOption(option =>
            option.setName('filter')
                .setDescription('Filter auctions')
                .setRequired(false)
                .addChoices(
                    { name: 'Active', value: 'active' },
                    { name: 'Ending Soon', value: 'ending_soon' },
                    { name: 'High Value', value: 'high_value' }
                )),
    
    cooldown: 5,

    async execute(interaction) {
        try {
            const filter = interaction.options.getString('filter') || 'active';

            await interaction.deferReply();

            // Get active auctions
            let auctions = await interaction.client.auctionService.getActiveAuctions();

            if (auctions.length === 0) {
                const embed = new EmbedBuilder()
                    .setTitle('📋 No Active Auctions')
                    .setColor(0xffaa00)
                    .setDescription('There are currently no active auctions.')
                    .addFields({
                        name: '💡 Tip',
                        value: 'Use `/open-auction` to start a new auction for a governor rank!',
                        inline: false
                    })
                    .setFooter({
                        text: 'Check back later for new auctions',
                        iconURL: interaction.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                return await interaction.editReply({ embeds: [embed] });
            }

            // Apply filters
            switch (filter) {
                case 'ending_soon':
                    auctions = auctions.filter(auction => {
                        const timeRemaining = new Date(auction.end_time) - new Date();
                        return timeRemaining <= 30 * 60 * 1000; // 30 minutes or less
                    });
                    break;
                case 'high_value':
                    auctions = auctions.filter(auction => auction.min_bid_amount >= 25000);
                    break;
                default:
                    // 'active' - no additional filtering needed
                    break;
            }

            if (auctions.length === 0) {
                const embed = new EmbedBuilder()
                    .setTitle(`📋 No Auctions Found`)
                    .setColor(0xffaa00)
                    .setDescription(`No auctions match the "${filter}" filter.`)
                    .setTimestamp();

                return await interaction.editReply({ embeds: [embed] });
            }

            // Create main embed
            const embed = new EmbedBuilder()
                .setTitle('🏛️ Active Governor Auctions')
                .setColor(0x00aaff)
                .setDescription(`Found **${auctions.length}** active auction${auctions.length !== 1 ? 's' : ''}`)
                .setFooter({
                    text: `Filter: ${filter} | Use the dropdown to view details`,
                    iconURL: interaction.client.user.displayAvatarURL()
                })
                .setTimestamp();

            // Add auction summary fields
            const auctionSummary = auctions.slice(0, 10).map(auction => {
                const timeRemaining = interaction.client.auctionService.getTimeRemaining(auction.end_time);
                const currentBid = auction.winning_bid ? 
                    `${auction.winning_bid.toLocaleString()}` : 
                    'No bids';
                
                return `**Rank ${auction.rank_number}** - ${auction.rank_name}\n` +
                       `💰 Current: ${currentBid} | Min: ${auction.min_bid_amount.toLocaleString()}\n` +
                       `⏰ ${timeRemaining} | 📊 ${auction.total_bids} bid${auction.total_bids !== 1 ? 's' : ''}`;
            }).join('\n\n');

            embed.addFields({
                name: '📋 Auction Summary',
                value: auctionSummary || 'No auctions to display',
                inline: false
            });

            // Create select menu for detailed view
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('auction_select')
                .setPlaceholder('Select an auction to view details')
                .addOptions(
                    auctions.slice(0, 25).map(auction => ({
                        label: `Rank ${auction.rank_number} - ${auction.rank_name}`,
                        description: `Min: ${auction.min_bid_amount.toLocaleString()} | Current: ${auction.winning_bid ? auction.winning_bid.toLocaleString() : 'No bids'}`,
                        value: auction.id.toString(),
                        emoji: '🏛️'
                    }))
                );

            const actionRow = new ActionRowBuilder().addComponents(selectMenu);

            // Add statistics
            const totalMinBids = auctions.reduce((sum, auction) => sum + auction.min_bid_amount, 0);
            const totalCurrentBids = auctions.reduce((sum, auction) => sum + (auction.winning_bid || 0), 0);
            const totalBids = auctions.reduce((sum, auction) => sum + auction.total_bids, 0);

            embed.addFields(
                {
                    name: '📊 Statistics',
                    value: `**Total Min Bids:** ${totalMinBids.toLocaleString()}\n` +
                           `**Total Current Bids:** ${totalCurrentBids.toLocaleString()}\n` +
                           `**Total Bid Count:** ${totalBids}`,
                    inline: true
                },
                {
                    name: '🏆 Highest Ranks',
                    value: auctions.slice(0, 3).map(auction => 
                        `Rank ${auction.rank_number}: ${auction.rank_name}`
                    ).join('\n') || 'None',
                    inline: true
                }
            );

            await interaction.editReply({
                embeds: [embed],
                components: [actionRow]
            });

        } catch (error) {
            logger.error('Error in auctions command:', error);
            
            const errorMessage = {
                content: '❌ An error occurred while fetching auction information.',
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ ...errorMessage, ephemeral: true });
            }
        }
    },
};
