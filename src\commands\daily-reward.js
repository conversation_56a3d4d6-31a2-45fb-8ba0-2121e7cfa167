const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const logger = require('../utils/logger');
const moment = require('moment');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('daily-reward')
        .setDescription('Claim your daily currency reward'),
    
    cooldown: 60, // 1 minute cooldown to prevent spam

    async execute(interaction) {
        try {
            await interaction.deferReply({ ephemeral: true });

            // Ensure user exists in database
            await interaction.client.currencyService.ensureUser(
                interaction.user.id,
                interaction.user.username,
                interaction.user.discriminator
            );

            // Check if user can claim daily reward
            const canClaim = await interaction.client.currencyService.canClaimDailyReward(interaction.user.id);

            if (!canClaim) {
                // Get user's last claim time
                const user = await interaction.client.database.get(
                    'SELECT last_daily_reward FROM users WHERE discord_id = ?',
                    [interaction.user.id]
                );

                const lastClaim = moment(user.last_daily_reward);
                const nextClaim = lastClaim.clone().add(1, 'day');
                const timeUntilNext = moment.duration(nextClaim.diff(moment()));

                const embed = new EmbedBuilder()
                    .setTitle('⏰ Daily Reward Already Claimed')
                    .setColor(0xffaa00)
                    .setDescription('You have already claimed your daily reward today!')
                    .addFields(
                        {
                            name: '📅 Last Claimed',
                            value: `<t:${Math.floor(lastClaim.unix())}:F>`,
                            inline: true
                        },
                        {
                            name: '🕐 Next Available',
                            value: `<t:${Math.floor(nextClaim.unix())}:R>`,
                            inline: true
                        },
                        {
                            name: '⏳ Time Remaining',
                            value: `${Math.floor(timeUntilNext.asHours())}h ${timeUntilNext.minutes()}m`,
                            inline: true
                        }
                    )
                    .setFooter({
                        text: 'Come back tomorrow for your next reward!',
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                return await interaction.editReply({ embeds: [embed] });
            }

            // Claim the daily reward
            const result = await interaction.client.currencyService.claimDailyReward(interaction.user.id);

            if (result.success) {
                // Get user's new balance
                const newBalance = await interaction.client.currencyService.getBalance(interaction.user.id);

                const embed = new EmbedBuilder()
                    .setTitle('🎉 Daily Reward Claimed!')
                    .setColor(0x00ff00)
                    .setDescription(`You have successfully claimed your daily reward!`)
                    .addFields(
                        {
                            name: '💰 Reward Amount',
                            value: `${result.amount.toLocaleString()} currency`,
                            inline: true
                        },
                        {
                            name: '💳 New Balance',
                            value: `${newBalance.toLocaleString()} currency`,
                            inline: true
                        },
                        {
                            name: '📅 Claimed On',
                            value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                            inline: true
                        },
                        {
                            name: '🔄 Next Reward',
                            value: `<t:${Math.floor((Date.now() + 24 * 60 * 60 * 1000) / 1000)}:R>`,
                            inline: false
                        }
                    )
                    .setFooter({
                        text: 'Remember to claim your reward daily!',
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await interaction.editReply({ embeds: [embed] });

                // Log the daily reward claim
                logger.currency('daily_reward', interaction.user.id, result.amount, newBalance, {
                    username: interaction.user.username,
                    guildId: interaction.guild?.id,
                    channelId: interaction.channel?.id
                });

                // Check for streak bonuses (future feature)
                // This could be implemented to give bonus rewards for consecutive days
                
            } else {
                const embed = new EmbedBuilder()
                    .setTitle('❌ Failed to Claim Reward')
                    .setColor(0xff0000)
                    .setDescription('There was an error claiming your daily reward. Please try again.')
                    .setFooter({
                        text: 'If this problem persists, contact an administrator.',
                        iconURL: interaction.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await interaction.editReply({ embeds: [embed] });
            }
        } catch (error) {
            logger.error('Error in daily-reward command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setTitle('❌ Error')
                .setColor(0xff0000)
                .setDescription('An unexpected error occurred while processing your daily reward.')
                .setFooter({
                    text: 'Please try again later or contact support.',
                    iconURL: interaction.user.displayAvatarURL()
                })
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },
};
