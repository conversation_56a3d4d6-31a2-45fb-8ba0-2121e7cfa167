const { SlashCommandBuilder, PermissionFlagsBits, EmbedBuilder } = require('discord.js');
const logger = require('../utils/logger');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('open-auction')
        .setDescription('Start a new governor rank auction')
        .addIntegerOption(option =>
            option.setName('rank')
                .setDescription('Governor rank number (1-15)')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(15))
        .addIntegerOption(option =>
            option.setName('duration')
                .setDescription('Auction duration in minutes (5-60)')
                .setRequired(false)
                .setMinValue(5)
                .setMaxValue(60))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages),
    
    cooldown: 10,

    async execute(interaction) {
        try {
            const rankNumber = interaction.options.getInteger('rank');
            const durationMinutes = interaction.options.getInteger('duration') || 30;
            const durationMs = durationMinutes * 60 * 1000;

            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageMessages)) {
                return await interaction.reply({
                    content: '❌ You need Manage Messages permissions to create auctions.',
                    ephemeral: true
                });
            }

            await interaction.deferReply();

            // Create the auction
            const result = await interaction.client.auctionService.createAuction(
                rankNumber,
                interaction.user.id,
                durationMs
            );

            if (result.success) {
                // Get full auction details for embed
                const auction = await interaction.client.auctionService.getAuctionById(result.auctionId);
                
                // Create auction embed
                const embed = await interaction.client.auctionService.createAuctionEmbed(auction);
                
                // Create action buttons
                const actionRow = interaction.client.auctionService.createAuctionButtons(auction);

                const components = actionRow ? [actionRow] : [];

                // Send auction announcement
                const auctionMessage = await interaction.editReply({
                    content: `🚨 **NEW AUCTION STARTED!** 🚨\n\n${interaction.user} has started an auction for **${auction.rank_name}**!`,
                    embeds: [embed],
                    components
                });

                // Log auction creation
                logger.auction('created', result.auctionId, interaction.user.id, {
                    rankNumber,
                    duration: durationMinutes,
                    guildId: interaction.guild.id,
                    channelId: interaction.channel.id,
                    messageId: auctionMessage.id
                });

                // Send confirmation to creator
                const confirmEmbed = new EmbedBuilder()
                    .setTitle('✅ Auction Created Successfully')
                    .setColor(0x00ff00)
                    .setDescription(`Your auction for **${auction.rank_name}** has been started!`)
                    .addFields(
                        {
                            name: '🎯 Rank',
                            value: `#${auction.rank_number} - ${auction.rank_name}`,
                            inline: true
                        },
                        {
                            name: '⏰ Duration',
                            value: `${durationMinutes} minutes`,
                            inline: true
                        },
                        {
                            name: '💰 Minimum Bid',
                            value: `${auction.min_bid_amount.toLocaleString()} currency`,
                            inline: true
                        },
                        {
                            name: '🆔 Auction ID',
                            value: result.auctionId.toString(),
                            inline: true
                        },
                        {
                            name: '🏁 Ends At',
                            value: `<t:${Math.floor(new Date(result.endTime).getTime() / 1000)}:F>`,
                            inline: true
                        }
                    )
                    .setFooter({
                        text: 'Players can now start bidding on this auction!',
                        iconURL: interaction.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                await interaction.followUp({
                    embeds: [confirmEmbed],
                    ephemeral: true
                });

            } else {
                await interaction.editReply({
                    content: `❌ Failed to create auction: ${result.error}`,
                });
            }
        } catch (error) {
            logger.error('Error in open-auction command:', error);
            
            const errorMessage = {
                content: '❌ An error occurred while creating the auction. Please try again.',
            };

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply(errorMessage);
            }
        }
    },
};
