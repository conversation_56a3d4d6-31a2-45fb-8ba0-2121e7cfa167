const logger = require('./logger');

class SecurityManager {
    constructor() {
        this.rateLimits = new Map(); // userId -> { command -> { count, resetTime } }
        this.suspiciousActivity = new Map(); // userId -> { violations, lastViolation }
        this.bannedUsers = new Set();
        
        // Rate limiting configuration
        this.rateLimitConfig = {
            'add-currency': { maxRequests: 5, windowMs: 60000 }, // 5 requests per minute
            'open-auction': { maxRequests: 3, windowMs: 300000 }, // 3 requests per 5 minutes
            'daily-reward': { maxRequests: 2, windowMs: 86400000 }, // 2 requests per day
            'balance': { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute
            'auctions': { maxRequests: 15, windowMs: 60000 }, // 15 requests per minute
            'tasks': { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute
            'default': { maxRequests: 20, windowMs: 60000 } // Default: 20 requests per minute
        };

        // Security thresholds
        this.securityThresholds = {
            maxViolationsPerHour: 10,
            maxCurrencyPerTransaction: 1000000,
            maxBidAmount: 10000000,
            suspiciousPatternThreshold: 5
        };

        // Clean up old rate limit data every 5 minutes
        setInterval(() => this.cleanupRateLimits(), 300000);
    }

    /**
     * Check if user is rate limited for a command
     */
    checkRateLimit(userId, commandName) {
        const now = Date.now();
        const config = this.rateLimitConfig[commandName] || this.rateLimitConfig.default;

        if (!this.rateLimits.has(userId)) {
            this.rateLimits.set(userId, new Map());
        }

        const userLimits = this.rateLimits.get(userId);
        
        if (!userLimits.has(commandName)) {
            userLimits.set(commandName, { count: 0, resetTime: now + config.windowMs });
        }

        const commandLimit = userLimits.get(commandName);

        // Reset if window has expired
        if (now > commandLimit.resetTime) {
            commandLimit.count = 0;
            commandLimit.resetTime = now + config.windowMs;
        }

        // Check if limit exceeded
        if (commandLimit.count >= config.maxRequests) {
            const timeUntilReset = Math.ceil((commandLimit.resetTime - now) / 1000);
            
            logger.security('rate_limit_exceeded', userId, {
                command: commandName,
                count: commandLimit.count,
                maxRequests: config.maxRequests,
                timeUntilReset
            });

            return {
                limited: true,
                timeUntilReset,
                maxRequests: config.maxRequests,
                currentCount: commandLimit.count
            };
        }

        // Increment counter
        commandLimit.count++;

        return {
            limited: false,
            currentCount: commandLimit.count,
            maxRequests: config.maxRequests,
            timeUntilReset: Math.ceil((commandLimit.resetTime - now) / 1000)
        };
    }

    /**
     * Validate currency transaction
     */
    validateCurrencyTransaction(amount, transactionType, userId) {
        const errors = [];

        // Check amount bounds
        if (amount <= 0) {
            errors.push('Amount must be positive');
        }

        if (amount > this.securityThresholds.maxCurrencyPerTransaction) {
            errors.push(`Amount exceeds maximum allowed (${this.securityThresholds.maxCurrencyPerTransaction.toLocaleString()})`);
            this.reportSuspiciousActivity(userId, 'excessive_currency_transaction', { amount, transactionType });
        }

        // Check for suspicious patterns
        if (this.detectSuspiciousPattern(userId, 'currency_transaction', amount)) {
            errors.push('Suspicious transaction pattern detected');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Validate auction bid
     */
    validateAuctionBid(bidAmount, minBidAmount, userBalance, userId) {
        const errors = [];

        // Basic validation
        if (bidAmount <= 0) {
            errors.push('Bid amount must be positive');
        }

        if (bidAmount < minBidAmount) {
            errors.push(`Bid must be at least ${minBidAmount.toLocaleString()} currency`);
        }

        if (bidAmount > userBalance) {
            errors.push('Insufficient currency for bid');
        }

        if (bidAmount > this.securityThresholds.maxBidAmount) {
            errors.push(`Bid exceeds maximum allowed (${this.securityThresholds.maxBidAmount.toLocaleString()})`);
            this.reportSuspiciousActivity(userId, 'excessive_bid_amount', { bidAmount });
        }

        // Check for bid manipulation patterns
        if (this.detectSuspiciousPattern(userId, 'auction_bid', bidAmount)) {
            errors.push('Suspicious bidding pattern detected');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Sanitize user input
     */
    sanitizeInput(input, maxLength = 1000) {
        if (typeof input !== 'string') {
            return String(input);
        }

        return input
            .trim()
            .substring(0, maxLength)
            .replace(/[<>]/g, '') // Remove potential HTML/XML tags
            .replace(/[@#]/g, ''); // Remove Discord mention characters
    }

    /**
     * Validate Discord user permissions
     */
    validatePermissions(member, requiredPermissions) {
        if (!member || !member.permissions) {
            return { valid: false, error: 'Unable to verify permissions' };
        }

        const hasPermissions = requiredPermissions.every(permission => 
            member.permissions.has(permission)
        );

        if (!hasPermissions) {
            logger.security('permission_denied', member.user.id, {
                requiredPermissions: requiredPermissions.map(p => p.toString()),
                userPermissions: member.permissions.toArray()
            });

            return { valid: false, error: 'Insufficient permissions' };
        }

        return { valid: true };
    }

    /**
     * Detect suspicious activity patterns
     */
    detectSuspiciousPattern(userId, activityType, value) {
        const now = Date.now();
        const key = `${userId}_${activityType}`;
        
        if (!this.suspiciousActivity.has(key)) {
            this.suspiciousActivity.set(key, { activities: [], violations: 0 });
        }

        const userActivity = this.suspiciousActivity.get(key);
        
        // Add current activity
        userActivity.activities.push({ timestamp: now, value });
        
        // Keep only activities from last hour
        userActivity.activities = userActivity.activities.filter(
            activity => now - activity.timestamp < 3600000
        );

        // Check for suspicious patterns
        let suspicious = false;

        // Pattern 1: Too many activities in short time
        if (userActivity.activities.length > this.securityThresholds.suspiciousPatternThreshold) {
            suspicious = true;
        }

        // Pattern 2: Identical values repeated
        if (activityType === 'currency_transaction' || activityType === 'auction_bid') {
            const recentValues = userActivity.activities
                .filter(a => now - a.timestamp < 300000) // Last 5 minutes
                .map(a => a.value);
            
            const uniqueValues = new Set(recentValues);
            if (recentValues.length >= 3 && uniqueValues.size === 1) {
                suspicious = true;
            }
        }

        if (suspicious) {
            userActivity.violations++;
            this.reportSuspiciousActivity(userId, 'pattern_detection', {
                activityType,
                recentActivities: userActivity.activities.length,
                violations: userActivity.violations
            });
        }

        return suspicious;
    }

    /**
     * Report suspicious activity
     */
    reportSuspiciousActivity(userId, activityType, details) {
        logger.security('suspicious_activity', userId, {
            activityType,
            details,
            timestamp: new Date().toISOString()
        });

        // Auto-ban for severe violations
        if (details.violations && details.violations >= this.securityThresholds.maxViolationsPerHour) {
            this.bannedUsers.add(userId);
            logger.security('auto_ban', userId, {
                reason: 'Excessive violations',
                violations: details.violations
            });
        }
    }

    /**
     * Check if user is banned
     */
    isUserBanned(userId) {
        return this.bannedUsers.has(userId);
    }

    /**
     * Ban a user
     */
    banUser(userId, reason = 'Security violation') {
        this.bannedUsers.add(userId);
        logger.security('manual_ban', userId, { reason });
    }

    /**
     * Unban a user
     */
    unbanUser(userId) {
        this.bannedUsers.delete(userId);
        logger.security('unban', userId, {});
    }

    /**
     * Clean up old rate limit data
     */
    cleanupRateLimits() {
        const now = Date.now();
        let cleaned = 0;

        for (const [userId, userLimits] of this.rateLimits.entries()) {
            for (const [command, limit] of userLimits.entries()) {
                if (now > limit.resetTime + 300000) { // 5 minutes after reset
                    userLimits.delete(command);
                    cleaned++;
                }
            }
            
            if (userLimits.size === 0) {
                this.rateLimits.delete(userId);
            }
        }

        if (cleaned > 0) {
            logger.info(`Cleaned up ${cleaned} expired rate limit entries`);
        }
    }

    /**
     * Get security statistics
     */
    getSecurityStats() {
        return {
            rateLimitedUsers: this.rateLimits.size,
            bannedUsers: this.bannedUsers.size,
            suspiciousActivities: this.suspiciousActivity.size,
            totalViolations: Array.from(this.suspiciousActivity.values())
                .reduce((sum, activity) => sum + activity.violations, 0)
        };
    }

    /**
     * Validate auction creation parameters
     */
    validateAuctionCreation(rankNumber, duration, creatorId) {
        const errors = [];

        // Validate rank number
        if (!Number.isInteger(rankNumber) || rankNumber < 1 || rankNumber > 15) {
            errors.push('Rank number must be between 1 and 15');
        }

        // Validate duration
        const minDuration = 300000; // 5 minutes
        const maxDuration = 3600000; // 1 hour
        
        if (duration < minDuration || duration > maxDuration) {
            errors.push(`Duration must be between ${minDuration/1000} and ${maxDuration/1000} seconds`);
        }

        // Check for auction spam
        if (this.detectSuspiciousPattern(creatorId, 'auction_creation', rankNumber)) {
            errors.push('Too many auctions created recently');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }
}

module.exports = new SecurityManager();
