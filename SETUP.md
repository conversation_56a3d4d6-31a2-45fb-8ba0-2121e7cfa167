# Setup Guide - Rise of Kingdoms Governor Au<PERSON>

This guide will walk you through setting up the ROK Governor Auction Bo<PERSON> from scratch.

## 📋 Prerequisites

Before you begin, ensure you have:

- **Node.js 16.0.0 or higher** - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js)
- **Discord account** with server admin permissions
- **Basic command line knowledge**

## 🤖 Discord Bot Setup

### Step 1: Create Discord Application

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application"
3. Enter a name (e.g., "ROK Governor Bot")
4. Click "Create"

### Step 2: Create Bot User

1. In your application, go to the "Bot" section
2. Click "Add Bot"
3. Confirm by clicking "Yes, do it!"
4. **Copy the bot token** (you'll need this later)
5. Under "Privileged Gateway Intents", enable:
   - Server Members Intent
   - Message Content Intent

### Step 3: Set Bot Permissions

1. Go to the "OAuth2" > "URL Generator" section
2. Select scopes:
   - `bot`
   - `applications.commands`
3. Select bot permissions:
   - Send Messages
   - Use Slash Commands
   - Embed Links
   - Read Message History
   - Add Reactions
   - Manage Messages (for auction management)
4. Copy the generated URL and use it to invite the bot to your server

## 💻 Local Setup

### Step 1: Download and Install

```bash
# Clone the repository (or download the files)
git clone <repository-url>
cd rok-governor-auction-bot

# Install dependencies
npm install
```

### Step 2: Environment Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:
   ```env
   # Required: Discord Bot Configuration
   DISCORD_TOKEN=your_bot_token_here
   CLIENT_ID=your_application_client_id
   GUILD_ID=your_server_id_for_testing

   # Optional: Customize these values
   DAILY_REWARD_AMOUNT=5000
   MAX_CURRENCY_PER_USER=10000000
   
   # Auction settings
   DEFAULT_AUCTION_DURATION=1800000
   
   # Governor rank minimum bids
   RANK_1_MIN_BID=50000
   RANK_2_MIN_BID=45000
   # ... etc
   ```

### Step 3: Get Required IDs

#### Bot Token
- From Discord Developer Portal > Your App > Bot > Token

#### Client ID
- From Discord Developer Portal > Your App > General Information > Application ID

#### Guild ID (Server ID)
1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click your server name
3. Click "Copy ID"

### Step 4: Deploy Commands

```bash
# Deploy slash commands to Discord
npm run deploy
```

You should see output like:
```
Started refreshing 6 application (/) commands.
Successfully reloaded 6 guild application (/) commands.
```

### Step 5: Start the Bot

```bash
# Start the bot
npm start
```

You should see:
```
Ready! Logged in as YourBotName#1234
Connected to SQLite database
Database initialized successfully
ROK Auction Bot initialized successfully
```

## 🔧 Configuration Options

### Currency Settings

```env
# Daily reward amount
DAILY_REWARD_AMOUNT=5000

# Maximum currency a user can hold
MAX_CURRENCY_PER_USER=10000000
```

### Auction Settings

```env
# Minimum auction duration (5 minutes)
AUCTION_MIN_DURATION=300000

# Maximum auction duration (1 hour)
AUCTION_MAX_DURATION=3600000

# Default duration (30 minutes)
DEFAULT_AUCTION_DURATION=1800000
```

### Governor Rank Configuration

Set minimum bid amounts for each rank:

```env
RANK_1_MIN_BID=50000   # Supreme Governor
RANK_2_MIN_BID=45000   # Grand Governor
RANK_3_MIN_BID=40000   # Elite Governor
# ... continue for all 15 ranks
RANK_15_MIN_BID=5000   # Recruit Governor
```

### Security Settings

```env
# Rate limiting
RATE_LIMIT_WINDOW=60000        # 1 minute window
RATE_LIMIT_MAX_REQUESTS=10     # Max requests per window

# Admin role names
ADMIN_ROLE_NAME=Admin
MODERATOR_ROLE_NAME=Moderator
```

## 🗄️ Database Setup

The bot automatically creates and manages its SQLite database. The database file will be created at:
- `./data/bot.db` (default)
- Or the path specified in `DATABASE_PATH` environment variable

### Database Tables

The bot creates these tables automatically:
- `users` - User accounts and currency
- `auctions` - Auction records
- `bids` - Bid history
- `currency_transactions` - Transaction log
- `work_tasks` - Task definitions
- `user_task_completions` - Task progress
- `governor_ranks` - Rank configurations
- `bot_settings` - Bot configuration

## 🧪 Testing the Setup

### Test Basic Functionality

1. **Check bot status**: The bot should appear online in your server
2. **Test slash commands**: Type `/` and you should see the bot's commands
3. **Test daily reward**: Use `/daily-reward` to claim currency
4. **Check balance**: Use `/balance` to see your currency

### Test Auction System

1. **Create auction**: Use `/open-auction rank:1 duration:5`
2. **View auctions**: Use `/auctions` to see active auctions
3. **Place bids**: Click the bid buttons on auction messages

### Test Work System

1. **View tasks**: Use `/tasks` to see available tasks
2. **Complete tasks**: Click task completion buttons
3. **Check progress**: View your task completion statistics

## 🚨 Troubleshooting

### Common Issues

#### Bot doesn't respond to commands
- Check if bot is online
- Verify bot has necessary permissions
- Ensure commands were deployed (`npm run deploy`)
- Check console for error messages

#### Database errors
- Ensure `data/` directory exists and is writable
- Check file permissions
- Verify SQLite is working

#### Permission errors
- Verify bot has required permissions in Discord
- Check role hierarchy (bot role should be high enough)
- Ensure bot can send messages in the channel

#### Rate limiting issues
- Check if you're hitting rate limits
- Adjust rate limit settings in `.env`
- Wait for rate limit to reset

### Debug Mode

Enable debug logging:
```env
LOG_LEVEL=debug
```

This will provide more detailed logs to help identify issues.

### Log Files

Check these log files for errors:
- `logs/error.log` - Error messages only
- `logs/combined.log` - All log messages

## 🔄 Updates and Maintenance

### Updating the Bot

1. **Backup your data**:
   ```bash
   cp -r data/ data_backup/
   ```

2. **Update code**:
   ```bash
   git pull origin main
   npm install
   ```

3. **Deploy new commands** (if any):
   ```bash
   npm run deploy
   ```

4. **Restart the bot**:
   ```bash
   npm start
   ```

### Regular Maintenance

- **Monitor logs** for errors or warnings
- **Backup database** regularly
- **Update dependencies** periodically
- **Review security logs** for suspicious activity

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** in the `logs/` directory
2. **Review this setup guide** for missed steps
3. **Check the main README.md** for additional information
4. **Create an issue** with detailed error information
5. **Contact support** with log files and configuration details

## 🎉 Success!

Once everything is working, you should have:
- ✅ Bot online and responding to commands
- ✅ Currency system functional
- ✅ Auction system working
- ✅ Work management system active
- ✅ All security features enabled

Your Rise of Kingdoms Governor Auction Bot is now ready to use!
