# Rise of Kingdoms Governor Auction Bot

A comprehensive Discord bot with work management system and currency-based auction system for Rise of Kingdoms governor positions.

## 🌟 Features

### 💰 Currency System
- Virtual currency for players to earn and spend
- Daily reward system (5,000 currency per day)
- Transaction logging and audit trail
- Anti-cheat measures and security controls
- Currency leaderboards and statistics

### 🏛️ Auction System
- Support for 15 different governor ranks
- Real-time bidding with automatic refunds
- Beautiful Discord embeds with rich formatting
- Flexible bidding system with rank-based minimum bids
- Auction timers and automatic completion
- Multiple players can bid simultaneously

### 📋 Work Management
- Daily, weekly, and monthly task tracking
- Currency rewards for task completion
- Progress monitoring and statistics
- Automated task resets via cron jobs
- Task leaderboards and achievements

### 🛡️ Security & Safety
- Rate limiting to prevent spam
- Input validation and sanitization
- Suspicious activity detection
- Comprehensive error handling
- Permission-based command access
- Anti-exploit measures

## 🚀 Quick Start

### Prerequisites
- Node.js 16.0.0 or higher
- Discord Bot Token
- Discord Application with slash commands enabled

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd rok-governor-auction-bot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your Discord bot token and configuration
   ```

4. **Deploy slash commands**
   ```bash
   npm run deploy
   ```

5. **Start the bot**
   ```bash
   npm start
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_client_id_here
GUILD_ID=your_discord_server_guild_id_here

# Database Configuration
DATABASE_PATH=./data/bot.db

# Currency Configuration
DAILY_REWARD_AMOUNT=5000
MAX_CURRENCY_PER_USER=10000000

# Auction Configuration
AUCTION_MIN_DURATION=300000
AUCTION_MAX_DURATION=3600000
DEFAULT_AUCTION_DURATION=1800000

# Governor Ranks (minimum bid amounts)
RANK_1_MIN_BID=50000
RANK_2_MIN_BID=45000
# ... (see .env.example for all ranks)
```

### Governor Ranks

The bot supports 15 governor ranks with configurable minimum bid amounts:

| Rank | Title | Default Min Bid |
|------|-------|----------------|
| 1 | Supreme Governor | 50,000 |
| 2 | Grand Governor | 45,000 |
| 3 | Elite Governor | 40,000 |
| ... | ... | ... |
| 15 | Recruit Governor | 5,000 |

## 📖 Commands

### Essential Commands

- `/add-currency [user] [amount] [reason]` - Add currency to a user (Admin only)
- `/open-auction [rank] [duration]` - Start a new governor rank auction
- `/daily-reward` - Claim your daily currency reward

### User Commands

- `/balance [user]` - Check currency balance
- `/auctions [filter]` - View active auctions
- `/tasks [type]` - View and manage work tasks

### Admin Commands

- `/add-currency` - Add currency to users
- Various auction management commands

## 🎨 Message Design

The bot features beautiful Discord embeds with:
- Rich colors and professional styling
- Relevant emojis for visual appeal
- Consistent branding and typography
- Interactive buttons and select menus
- Real-time updates and notifications

## 🏗️ Architecture

### Project Structure
```
src/
├── commands/          # Slash commands
├── events/           # Discord event handlers
├── services/         # Business logic services
├── utils/           # Utility functions
├── database/        # Database schema and management
└── index.js         # Main bot file
```

### Services

- **CurrencyService**: Manages virtual currency operations
- **AuctionService**: Handles auction creation, bidding, and completion
- **WorkService**: Manages daily/weekly/monthly tasks

### Database

SQLite database with the following main tables:
- `users` - User information and currency balances
- `auctions` - Auction records and status
- `bids` - Individual bid records
- `currency_transactions` - Transaction audit trail
- `work_tasks` - Task definitions
- `user_task_completions` - Task completion records

## 🔧 Development

### Scripts

- `npm start` - Start the bot in production mode
- `npm run dev` - Start with nodemon for development
- `npm run deploy` - Deploy slash commands to Discord
- `npm test` - Run tests (when implemented)
- `npm run lint` - Run ESLint

### Adding New Commands

1. Create a new file in `src/commands/`
2. Follow the existing command structure
3. Export the command with `data` and `execute` properties
4. Run `npm run deploy` to register the command

### Adding New Features

1. Create service classes in `src/services/`
2. Add database schema changes to `src/database/schema.sql`
3. Update the main bot file to initialize new services
4. Add appropriate error handling and security checks

## 🛡️ Security Features

- **Rate Limiting**: Prevents command spam and abuse
- **Input Validation**: Sanitizes all user inputs
- **Permission Checks**: Ensures users have required permissions
- **Audit Logging**: Tracks all important actions
- **Anti-Cheat**: Detects suspicious patterns and behaviors
- **Error Handling**: Graceful error recovery and user feedback

## 📊 Monitoring & Logging

The bot includes comprehensive logging:
- Command execution logs
- Currency transaction logs
- Auction activity logs
- Security event logs
- Error logs with stack traces

Logs are stored in the `logs/` directory with automatic rotation.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please:
1. Check the documentation
2. Review the logs for error details
3. Create an issue with detailed information
4. Contact the development team

## 🔄 Updates & Maintenance

- Regular security updates
- Feature enhancements based on user feedback
- Performance optimizations
- Bug fixes and stability improvements

---

**Made with ❤️ for the Rise of Kingdoms community**
