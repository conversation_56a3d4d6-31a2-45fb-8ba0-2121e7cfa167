const { EmbedBuilder } = require('discord.js');
const logger = require('./logger');

class ErrorHandler {
    constructor() {
        this.errorTypes = {
            VALIDATION_ERROR: 'validation',
            PERMISSION_ERROR: 'permission',
            RATE_LIMIT_ERROR: 'rate_limit',
            DATABASE_ERROR: 'database',
            CURRENCY_ERROR: 'currency',
            AUCTION_ERROR: 'auction',
            SECURITY_ERROR: 'security',
            UNKNOWN_ERROR: 'unknown'
        };

        this.userFriendlyMessages = {
            validation: 'Invalid input provided. Please check your command parameters.',
            permission: 'You don\'t have permission to use this command.',
            rate_limit: 'You\'re doing that too often. Please wait before trying again.',
            database: 'A database error occurred. Please try again later.',
            currency: 'A currency-related error occurred. Please check your balance and try again.',
            auction: 'An auction-related error occurred. The auction may have ended or been cancelled.',
            security: 'This action was blocked for security reasons.',
            unknown: 'An unexpected error occurred. Please try again later.'
        };
    }

    /**
     * Handle command errors
     */
    async handleCommandError(interaction, error, commandName) {
        const errorId = this.generateErrorId();
        const errorType = this.classifyError(error);
        
        // Log the error with context
        logger.error(`Command error [${errorId}]:`, {
            error: error.message,
            stack: error.stack,
            command: commandName,
            userId: interaction.user.id,
            guildId: interaction.guild?.id,
            channelId: interaction.channel?.id,
            errorType
        });

        // Create user-friendly error embed
        const embed = this.createErrorEmbed(error, errorType, errorId);

        // Send error response
        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        } catch (responseError) {
            logger.error('Failed to send error response:', responseError);
        }

        // Report critical errors to administrators
        if (this.isCriticalError(error, errorType)) {
            await this.reportCriticalError(interaction, error, errorId, commandName);
        }
    }

    /**
     * Handle interaction errors (buttons, select menus, etc.)
     */
    async handleInteractionError(interaction, error, interactionType) {
        const errorId = this.generateErrorId();
        const errorType = this.classifyError(error);
        
        logger.error(`Interaction error [${errorId}]:`, {
            error: error.message,
            stack: error.stack,
            interactionType,
            customId: interaction.customId,
            userId: interaction.user.id,
            guildId: interaction.guild?.id,
            errorType
        });

        const embed = this.createErrorEmbed(error, errorType, errorId, true);

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        } catch (responseError) {
            logger.error('Failed to send interaction error response:', responseError);
        }
    }

    /**
     * Handle service errors (currency, auction, work services)
     */
    handleServiceError(error, serviceName, operation, context = {}) {
        const errorId = this.generateErrorId();
        const errorType = this.classifyError(error);
        
        logger.error(`Service error [${errorId}]:`, {
            error: error.message,
            stack: error.stack,
            service: serviceName,
            operation,
            context,
            errorType
        });

        return {
            success: false,
            error: this.userFriendlyMessages[errorType] || error.message,
            errorId,
            errorType
        };
    }

    /**
     * Classify error type
     */
    classifyError(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('validation') || message.includes('invalid')) {
            return this.errorTypes.VALIDATION_ERROR;
        }
        
        if (message.includes('permission') || message.includes('unauthorized')) {
            return this.errorTypes.PERMISSION_ERROR;
        }
        
        if (message.includes('rate limit') || message.includes('too many requests')) {
            return this.errorTypes.RATE_LIMIT_ERROR;
        }
        
        if (message.includes('database') || message.includes('sqlite') || message.includes('sql')) {
            return this.errorTypes.DATABASE_ERROR;
        }
        
        if (message.includes('currency') || message.includes('balance') || message.includes('insufficient')) {
            return this.errorTypes.CURRENCY_ERROR;
        }
        
        if (message.includes('auction') || message.includes('bid')) {
            return this.errorTypes.AUCTION_ERROR;
        }
        
        if (message.includes('security') || message.includes('suspicious') || message.includes('banned')) {
            return this.errorTypes.SECURITY_ERROR;
        }
        
        return this.errorTypes.UNKNOWN_ERROR;
    }

    /**
     * Create error embed
     */
    createErrorEmbed(error, errorType, errorId, isInteraction = false) {
        const embed = new EmbedBuilder()
            .setTitle(`❌ ${isInteraction ? 'Interaction' : 'Command'} Error`)
            .setColor(0xff0000)
            .setTimestamp();

        // Add user-friendly description
        const userMessage = this.userFriendlyMessages[errorType] || 'An unexpected error occurred.';
        embed.setDescription(userMessage);

        // Add specific error details for certain types
        if (errorType === this.errorTypes.RATE_LIMIT_ERROR) {
            embed.addFields({
                name: '⏰ Rate Limited',
                value: 'Please wait a moment before trying again. Rate limits help keep the bot responsive for everyone.',
                inline: false
            });
        } else if (errorType === this.errorTypes.PERMISSION_ERROR) {
            embed.addFields({
                name: '🔒 Permission Required',
                value: 'This command requires special permissions. Contact a server administrator if you believe this is an error.',
                inline: false
            });
        } else if (errorType === this.errorTypes.SECURITY_ERROR) {
            embed.addFields({
                name: '🛡️ Security Block',
                value: 'This action was blocked by our security system. If you believe this is an error, contact support.',
                inline: false
            });
        }

        // Add error ID for support
        embed.addFields({
            name: '🆔 Error ID',
            value: `\`${errorId}\``,
            inline: true
        });

        embed.setFooter({
            text: 'If this problem persists, please contact support with the Error ID above.'
        });

        return embed;
    }

    /**
     * Check if error is critical
     */
    isCriticalError(error, errorType) {
        return errorType === this.errorTypes.DATABASE_ERROR || 
               errorType === this.errorTypes.SECURITY_ERROR ||
               (errorType === this.errorTypes.UNKNOWN_ERROR && error.stack);
    }

    /**
     * Report critical error to administrators
     */
    async reportCriticalError(interaction, error, errorId, commandName) {
        try {
            // This could be enhanced to send DMs to administrators
            // or post to a dedicated error channel
            logger.error(`CRITICAL ERROR [${errorId}]:`, {
                error: error.message,
                stack: error.stack,
                command: commandName,
                user: `${interaction.user.username}#${interaction.user.discriminator}`,
                userId: interaction.user.id,
                guild: interaction.guild?.name,
                guildId: interaction.guild?.id,
                timestamp: new Date().toISOString()
            });
        } catch (reportError) {
            logger.error('Failed to report critical error:', reportError);
        }
    }

    /**
     * Generate unique error ID
     */
    generateErrorId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        return `${timestamp}-${random}`.toUpperCase();
    }

    /**
     * Create validation error response
     */
    createValidationError(field, message) {
        return {
            success: false,
            error: `Validation error: ${field} - ${message}`,
            errorType: this.errorTypes.VALIDATION_ERROR
        };
    }

    /**
     * Create permission error response
     */
    createPermissionError(requiredPermission) {
        return {
            success: false,
            error: `Permission denied: ${requiredPermission} required`,
            errorType: this.errorTypes.PERMISSION_ERROR
        };
    }

    /**
     * Create rate limit error response
     */
    createRateLimitError(timeUntilReset) {
        return {
            success: false,
            error: `Rate limited: Try again in ${timeUntilReset} seconds`,
            errorType: this.errorTypes.RATE_LIMIT_ERROR,
            timeUntilReset
        };
    }

    /**
     * Wrap async function with error handling
     */
    wrapAsync(fn) {
        return async (...args) => {
            try {
                return await fn(...args);
            } catch (error) {
                return this.handleServiceError(error, 'wrapped_function', fn.name);
            }
        };
    }

    /**
     * Get error statistics
     */
    getErrorStats() {
        // This could be enhanced to track error counts by type
        return {
            totalErrors: 0, // Would need to implement counting
            errorsByType: {},
            criticalErrors: 0
        };
    }
}

module.exports = new ErrorHandler();
